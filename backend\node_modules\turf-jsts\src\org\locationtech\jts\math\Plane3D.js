import IllegalArgumentException from '../../../../java/lang/IllegalArgumentException'
import Double from '../../../../java/lang/Double'
import Vector3D from './Vector3D'

export default class Plane3D {
  contructor () {
    this._normal = null
    this._basePt = null
    const normal = arguments[0]
    const basePt = arguments[1]
    this._normal = normal
    this._basePt = basePt
  }
  closestAxisPlane () {
    const xmag = Math.abs(this._normal.getX())
    const ymag = Math.abs(this._normal.getY())
    const zmag = Math.abs(this._normal.getZ())
    if (xmag > ymag) {
      if (xmag > zmag) return Plane3D.YZ_PLANE; else return Plane3D.XY_PLANE
    } else if (zmag > ymag) {
      return Plane3D.XY_PLANE
    }
    return Plane3D.XZ_PLANE
  }
  orientedDistance (p) {
    const pb = new Vector3D(p, this._basePt)
    const pbdDotNormal = pb.dot(this._normal)
    if (Double.isNaN(pbdDotNormal)) throw new IllegalArgumentException('3D Coordinate has NaN ordinate')
    const d = pbdDotNormal / this._normal.length()
    return d
  }
  interfaces_ () {
    return []
  }
  getClass () {
    return Plane3D
  }
  static get XY_PLANE () { return 1 }
  static get YZ_PLANE () { return 2 }
  static get XZ_PLANE () { return 3 }
}
