# @turf/isolines

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## isolines

Takes a grid [FeatureCollection][1] of [Point][2] features with z-values and an array of
value breaks and generates [isolines][3].

**Parameters**

-   `pointGrid` **[FeatureCollection][4]&lt;[Point][5]>** input points
-   `breaks` **[Array][6]&lt;[number][7]>** values of `zProperty` where to draw isolines
-   `options` **[Object][8]** Optional parameters (optional, default `{}`)
    -   `options.zProperty` **[string][9]** the property name in `points` from which z-values will be pulled (optional, default `'elevation'`)
    -   `options.commonProperties` **[Object][8]** GeoJSON properties passed to ALL isolines (optional, default `{}`)
    -   `options.breaksProperties` **[Array][6]&lt;[Object][8]>** GeoJSON properties passed, in order, to the correspondent isoline;
        the breaks array will define the order in which the isolines are created (optional, default `[]`)

**Examples**

```javascript
// create a grid of points with random z-values in their properties
var extent = [0, 30, 20, 50];
var cellWidth = 100;
var pointGrid = turf.pointGrid(extent, cellWidth, {units: 'miles'});

for (var i = 0; i < pointGrid.features.length; i++) {
    pointGrid.features[i].properties.temperature = Math.random() * 10;
}
var breaks = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10];

var lines = turf.isolines(pointGrid, breaks, {zProperty: 'temperature'});

//addToMap
var addToMap = [lines];
```

Returns **[FeatureCollection][4]&lt;[MultiLineString][10]>** a FeatureCollection of [MultiLineString][11] features representing isolines

[1]: https://tools.ietf.org/html/rfc7946#section-3.3

[2]: https://tools.ietf.org/html/rfc7946#section-3.1.2

[3]: http://en.wikipedia.org/wiki/Isoline

[4]: https://tools.ietf.org/html/rfc7946#section-3.3

[5]: https://tools.ietf.org/html/rfc7946#section-3.1.2

[6]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array

[7]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number

[8]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[9]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String

[10]: https://tools.ietf.org/html/rfc7946#section-3.1.5

[11]: https://tools.ietf.org/html/rfc7946#section-3.1.5

<!-- This file is automatically generated. Please don't edit it directly:
if you find an error, edit the source file (likely index.js), and re-run
./scripts/generate-readmes in the turf project. -->

---

This module is part of the [Turfjs project](http://turfjs.org/), an open source
module collection dedicated to geographic algorithms. It is maintained in the
[Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create
PRs and issues.

### Installation

Install this module individually:

```sh
$ npm install @turf/isolines
```

Or install the Turf module that includes it as a function:

```sh
$ npm install @turf/turf
```
