# @turf/intersect

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## intersect

Takes two [polygon][1] or [multi-polygon][2] geometries and finds their polygonal intersection. If they don't intersect, returns null.

**Parameters**

-   `poly1` **[Feature][3]&lt;([Polygon][4] \| [MultiPolygon][5])>** the first polygon or multipolygon
-   `poly2` **[Feature][3]&lt;([Polygon][4] \| [MultiPolygon][5])>** the second polygon or multipolygon
-   `options` **[Object][6]** Optional Parameters (optional, default `{}`)
    -   `options.properties` **[Object][6]** Translate GeoJSON Properties to Feature (optional, default `{}`)

**Examples**

```javascript
var poly1 = turf.polygon([[
  [-122.801742, 45.48565],
  [-122.801742, 45.60491],
  [-122.584762, 45.60491],
  [-122.584762, 45.48565],
  [-122.801742, 45.48565]
]]);

var poly2 = turf.polygon([[
  [-122.520217, 45.535693],
  [-122.64038, 45.553967],
  [-122.720031, 45.526554],
  [-122.669906, 45.507309],
  [-122.723464, 45.446643],
  [-122.532577, 45.408574],
  [-122.487258, 45.477466],
  [-122.520217, 45.535693]
]]);

var intersection = turf.intersect(poly1, poly2);

//addToMap
var addToMap = [poly1, poly2, intersection];
```

Returns **([Feature][3] | null)** returns a feature representing the area they share (either a [Polygon][1] or [MultiPolygon][2]). If they do not share any area, returns `null`.

[1]: https://tools.ietf.org/html/rfc7946#section-3.1.6

[2]: https://tools.ietf.org/html/rfc7946#section-3.1.7

[3]: https://tools.ietf.org/html/rfc7946#section-3.2

[4]: https://tools.ietf.org/html/rfc7946#section-3.1.6

[5]: https://tools.ietf.org/html/rfc7946#section-3.1.7

[6]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

<!-- This file is automatically generated. Please don't edit it directly:
if you find an error, edit the source file (likely index.js), and re-run
./scripts/generate-readmes in the turf project. -->

---

This module is part of the [Turfjs project](http://turfjs.org/), an open source
module collection dedicated to geographic algorithms. It is maintained in the
[Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create
PRs and issues.

### Installation

Install this module individually:

```sh
$ npm install @turf/intersect
```

Or install the Turf module that includes it as a function:

```sh
$ npm install @turf/turf
```
