# @turf/great-circle

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## greatCircle

Calculate great circles routes as [LineString][1] or [MultiLineString][2].
If the `start` and `end` points span the antimeridian, the resulting feature will
be split into a `MultiLineString`.

### Parameters

-   `start` **[Coord][3]** source point feature
-   `end` **[Coord][3]** destination point feature
-   `options` **[Object][4]** Optional parameters (optional, default `{}`)
    -   `options.properties` **[Object][4]** line feature properties (optional, default `{}`)
    -   `options.npoints` **[number][5]** number of points (optional, default `100`)
    -   `options.offset` **[number][5]** offset controls the likelyhood that lines will
        be split which cross the dateline. The higher the number the more likely. (optional, default `10`)

### Examples

```javascript
var start = turf.point([-122, 48]);
var end = turf.point([-77, 39]);

var greatCircle = turf.greatCircle(start, end, {properties: {name: 'Seattle to DC'}});

//addToMap
var addToMap = [start, end, greatCircle]
```

Returns **[Feature][6]&lt;([LineString][7] \| [MultiLineString][8])>** great circle line feature

[1]: https://tools.ietf.org/html/rfc7946#section-3.1.4

[2]: https://tools.ietf.org/html/rfc7946#section-3.1.5

[3]: https://tools.ietf.org/html/rfc7946#section-3.1.1

[4]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[5]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number

[6]: https://tools.ietf.org/html/rfc7946#section-3.2

[7]: https://tools.ietf.org/html/rfc7946#section-3.1.4

[8]: https://tools.ietf.org/html/rfc7946#section-3.1.5

<!-- This file is automatically generated. Please don't edit it directly:
if you find an error, edit the source file (likely index.js), and re-run
./scripts/generate-readmes in the turf project. -->

---

This module is part of the [Turfjs project](http://turfjs.org/), an open source
module collection dedicated to geographic algorithms. It is maintained in the
[Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create
PRs and issues.

### Installation

Install this module individually:

```sh
$ npm install @turf/great-circle
```

Or install the Turf module that includes it as a function:

```sh
$ npm install @turf/turf
```
