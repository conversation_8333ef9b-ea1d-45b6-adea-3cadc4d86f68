import React, { useState, useEffect, useRef } from 'react';
import { Map<PERSON>ontainer, TileLayer, Polygon, Polyline, LayersControl, useMap } from 'react-leaflet';
import 'leaflet/dist/leaflet.css';
import L from 'leaflet';
import 'leaflet-draw/dist/leaflet.draw.css';
import 'leaflet-draw';

// Fix for default markers
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-shadow.png',
});

// Component for drawing controls
function DrawingControls({ onBoundaryComplete }) {
  const map = useMap();
  const drawnItemsRef = useRef(L.featureGroup());

  useEffect(() => {
    if (!map) return;

    const drawnItems = drawnItemsRef.current;
    map.addLayer(drawnItems);

    // Initialize drawing controls
    const drawControl = new L.Control.Draw({
      position: 'topright',
      draw: {
        polygon: {
          shapeOptions: {
            color: '#3388ff',
            fillOpacity: 0.2,
            weight: 2
          },
          allowIntersection: false,
          showArea: true
        },
        polyline: {
          shapeOptions: {
            color: '#ff7800',
            weight: 3
          }
        },
        rectangle: {
          shapeOptions: {
            color: '#ff7800',
            fillOpacity: 0.1
          }
        },
        circle: false,
        circlemarker: false,
        marker: true
      },
      edit: {
        featureGroup: drawnItems,
        remove: true
      }
    });

    map.addControl(drawControl);

    // Handle drawing events
    const handleDrawCreated = (e) => {
      const layer = e.layer;
      drawnItems.addLayer(layer);

      if (e.layerType === 'polygon') {
        const points = layer.getLatLngs()[0].map(point => [point.lat, point.lng]);
        onBoundaryComplete(points);
      }
    };

    map.on(L.Draw.Event.CREATED, handleDrawCreated);

    return () => {
      map.removeControl(drawControl);
      map.removeLayer(drawnItems);
      map.off(L.Draw.Event.CREATED, handleDrawCreated);
    };
  }, [map, onBoundaryComplete]);

  return null;
}

// Component for grid rendering
function GridRenderer({ polygonPoints, gridConfig }) {
  const map = useMap();
  const gridLayerRef = useRef(null);

  useEffect(() => {
    if (!map || polygonPoints.length === 0) return;

    // Remove existing grid layer
    if (gridLayerRef.current) {
      map.removeLayer(gridLayerRef.current);
    }

    // Calculate grid based on polygon and config
    const gridLayer = L.layerGroup();

    // Get polygon bounds
    const polygon = L.polygon(polygonPoints);
    const bounds = polygon.getBounds();

    // Convert feet to meters (approximate)
    const eastWestMeters = gridConfig.eastWestUnit * 0.3048;
    const northSouthMeters = gridConfig.northSouthUnit * 0.3048;

    // Calculate grid lines
    const latStep = northSouthMeters / 111320; // Approximate meters to degrees
    const lngStep = eastWestMeters / (111320 * Math.cos(bounds.getCenter().lat * Math.PI / 180));

    // Draw vertical lines (East-West)
    for (let lng = bounds.getWest(); lng <= bounds.getEast(); lng += lngStep) {
      const line = L.polyline([
        [bounds.getNorth(), lng],
        [bounds.getSouth(), lng]
      ], {
        color: '#666',
        weight: 1,
        opacity: 0.5
      });
      gridLayer.addLayer(line);
    }

    // Draw horizontal lines (North-South)
    for (let lat = bounds.getSouth(); lat <= bounds.getNorth(); lat += latStep) {
      const line = L.polyline([
        [lat, bounds.getWest()],
        [lat, bounds.getEast()]
      ], {
        color: '#666',
        weight: 1,
        opacity: 0.5
      });
      gridLayer.addLayer(line);
    }

    gridLayerRef.current = gridLayer;
    map.addLayer(gridLayer);

    return () => {
      if (gridLayerRef.current) {
        map.removeLayer(gridLayerRef.current);
      }
    };
  }, [map, polygonPoints, gridConfig]);

  return null;
}

function DrawingCanvas({ onBoundaryComplete, gridConfig }) {
  const [polygonPoints, setPolygonPoints] = useState([]);
  const [canalData, setCanalData] = useState([]);

  // Sample canal data for demonstration
  useEffect(() => {
    // This would typically come from a GIS database or API
    const sampleCanals = [
      {
        id: 1,
        name: "Main Canal",
        coordinates: [
          [28.6139, 77.2090],
          [28.6150, 77.2100],
          [28.6160, 77.2110],
          [28.6170, 77.2120]
        ],
        type: "main"
      },
      {
        id: 2,
        name: "Branch Canal A",
        coordinates: [
          [28.6150, 77.2100],
          [28.6140, 77.2105],
          [28.6130, 77.2110]
        ],
        type: "branch"
      },
      {
        id: 3,
        name: "Distributary 1",
        coordinates: [
          [28.6140, 77.2105],
          [28.6135, 77.2095],
          [28.6130, 77.2085]
        ],
        type: "distributary"
      }
    ];
    setCanalData(sampleCanals);
  }, []);

  const handleBoundaryComplete = (points) => {
    setPolygonPoints(points);
    onBoundaryComplete(points);
  };

  const getCanalColor = (type) => {
    switch (type) {
      case 'main': return '#0066cc';
      case 'branch': return '#0099ff';
      case 'distributary': return '#33ccff';
      default: return '#0066cc';
    }
  };

  const getCanalWeight = (type) => {
    switch (type) {
      case 'main': return 6;
      case 'branch': return 4;
      case 'distributary': return 2;
      default: return 3;
    }
  };

  return (
    <div className="drawing-canvas">
      <MapContainer
        center={[28.6139, 77.2090]}  // Default to New Delhi coordinates
        zoom={15}
        style={{ height: '600px', width: '100%' }}
        ref={mapRef}
      >
        <LayersControl position="topright">
          <LayersControl.BaseLayer checked name="OpenStreetMap">
            <TileLayer
              url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
              attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            />
          </LayersControl.BaseLayer>

          <LayersControl.BaseLayer name="Satellite">
            <TileLayer
              url="https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"
              attribution='&copy; <a href="https://www.esri.com/">Esri</a>'
            />
          </LayersControl.BaseLayer>

          <LayersControl.BaseLayer name="Terrain">
            <TileLayer
              url="https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png"
              attribution='&copy; <a href="https://opentopomap.org/">OpenTopoMap</a>'
            />
          </LayersControl.BaseLayer>

          <LayersControl.Overlay checked name="Canal Network">
            <>
              {canalData.map((canal) => (
                <Polyline
                  key={canal.id}
                  positions={canal.coordinates}
                  color={getCanalColor(canal.type)}
                  weight={getCanalWeight(canal.type)}
                  opacity={0.8}
                />
              ))}
            </>
          </LayersControl.Overlay>

          <LayersControl.Overlay name="Chak Boundary">
            <>
              {polygonPoints.length > 0 && (
                <Polygon
                  positions={polygonPoints}
                  color="#3388ff"
                  fillOpacity={0.2}
                  weight={3}
                />
              )}
            </>
          </LayersControl.Overlay>
        </LayersControl>

        {/* Drawing Controls */}
        <DrawingControls onBoundaryComplete={handleBoundaryComplete} />

        {/* Grid Renderer */}
        <GridRenderer polygonPoints={polygonPoints} gridConfig={gridConfig} />
      </MapContainer>
    </div>
  );
}

export default DrawingCanvas;