import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, TileLayer, Polygon } from 'react-leaflet';
import 'leaflet/dist/leaflet.css';
import L from 'leaflet';
import 'leaflet-draw/dist/leaflet.draw.css';
import 'leaflet-draw';

function DrawingCanvas({ onBoundaryComplete, gridConfig }) {
  const mapRef = useRef(null);
  const [map, setMap] = useState(null);
  const [drawControl, setDrawControl] = useState(null);
  const [drawnItems] = useState(L.featureGroup());
  const [polygonPoints, setPolygonPoints] = useState([]);
  const [gridLayer, setGridLayer] = useState(null);

  // Initialize map and drawing controls
  useEffect(() => {
    if (!map) return;
    
    // Initialize drawing controls
    const drawControl = new L.Control.Draw({
      position: 'topright',
      draw: {
        polygon: {
          shapeOptions: {
            color: '#3388ff',
            fillOpacity: 0.2
          },
          allowIntersection: false,
          showArea: true
        },
        polyline: false,
        circle: false,
        rectangle: false,
        circlemarker: false,
        marker: false
      },
      edit: {
        featureGroup: drawnItems
      }
    });
    
    map.addControl(drawControl);
    setDrawControl(drawControl);
    
    // Handle drawing events
    map.on(L.Draw.Event.CREATED, (e) => {
      const layer = e.layer;
      drawnItems.addLayer(layer);
      
      if (e.layerType === 'polygon') {
        const points = layer.getLatLngs()[0].map(point => [point.lat, point.lng]);
        setPolygonPoints(points);
        onBoundaryComplete(points);
      }
    });
    
    return () => {
      if (drawControl) map.removeControl(drawControl);
    };
  }, [map, onBoundaryComplete, drawnItems]);

  // Render grid when polygon or config changes
  useEffect(() => {
    if (!map || polygonPoints.length === 0) return;
    
    // Remove existing grid layer
    if (gridLayer) {
      map.removeLayer(gridLayer);
    }
    
    // TODO: Implement grid rendering based on polygon and config
    // This will be added in the next iteration
    
  }, [map, polygonPoints, gridConfig]);

  return (
    <div className="drawing-canvas">
      <MapContainer
        center={[28.6139, 77.2090]}  // Default to New Delhi coordinates
        zoom={15}
        style={{ height: '500px', width: '100%' }}
        whenCreated={setMap}
        ref={mapRef}
      >
        <TileLayer
          url="https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png"
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors, &copy; <a href="https://carto.com/attributions">CARTO</a>'
        />
        {polygonPoints.length > 0 && (
          <Polygon
            positions={polygonPoints}
            color="#3388ff"
            fillOpacity={0.2}
          />
        )}
      </MapContainer>
    </div>
  );
}

export default DrawingCanvas;