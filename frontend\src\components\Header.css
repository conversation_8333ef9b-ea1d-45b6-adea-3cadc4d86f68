.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 0;
  margin-bottom: 30px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.logo-section {
  text-align: center;
  margin-bottom: 20px;
}

.logo-section h1 {
  font-size: 2.5rem;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.logo-section p {
  font-size: 1.1rem;
  margin: 5px 0 0 0;
  opacity: 0.9;
}

.navigation {
  display: flex;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
  margin-bottom: 30px;
}

.nav-item {
  background-color: rgba(255,255,255,0.1);
  padding: 10px 20px;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.2);
}

.nav-item:hover {
  background-color: rgba(255,255,255,0.2);
  transform: translateY(-2px);
}

.nav-item.active {
  background-color: rgba(255,255,255,0.3);
  border: 1px solid rgba(255,255,255,0.4);
}

.nav-item span {
  font-weight: 500;
}

.header-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.info-card {
  background-color: rgba(255,255,255,0.1);
  padding: 20px;
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.2);
}

.info-card h3 {
  margin: 0 0 15px 0;
  font-size: 1.2rem;
  color: #fff;
}

.info-card p {
  margin: 0;
  line-height: 1.6;
  opacity: 0.9;
}

.info-card ul {
  margin: 0;
  padding-left: 20px;
  line-height: 1.8;
}

.info-card li {
  opacity: 0.9;
  margin-bottom: 5px;
}

.info-card strong {
  color: #fff;
}

/* Responsive design */
@media (max-width: 768px) {
  .logo-section h1 {
    font-size: 2rem;
  }
  
  .navigation {
    flex-direction: column;
    align-items: center;
  }
  
  .nav-item {
    width: 100%;
    max-width: 300px;
    text-align: center;
  }
  
  .header-info {
    grid-template-columns: 1fr;
  }
}
