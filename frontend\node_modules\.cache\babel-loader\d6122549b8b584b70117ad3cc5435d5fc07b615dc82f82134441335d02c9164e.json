{"ast": null, "code": "import { createElementObject, createPathComponent, extendContext, updateCircle } from '@react-leaflet/core';\nimport { Circle as LeafletCircle } from 'leaflet';\nexport const Circle = createPathComponent(function createCircle({\n  center,\n  children: _c,\n  ...options\n}, ctx) {\n  const circle = new LeafletCircle(center, options);\n  return createElementObject(circle, extendContext(ctx, {\n    overlayContainer: circle\n  }));\n}, updateCircle);", "map": {"version": 3, "names": ["createElementObject", "createPathComponent", "extendContext", "updateCircle", "Circle", "LeafletCircle", "createCircle", "center", "children", "_c", "options", "ctx", "circle", "overlayContainer"], "sources": ["D:/Desktop files/My New Desktop/Irrigation Engineering and Structure Design/frontend/node_modules/react-leaflet/lib/Circle.js"], "sourcesContent": ["import { createElementObject, createPathComponent, extendContext, updateCircle } from '@react-leaflet/core';\nimport { Circle as LeafletCircle } from 'leaflet';\nexport const Circle = createPathComponent(function createCircle({ center , children: _c , ...options }, ctx) {\n    const circle = new LeafletCircle(center, options);\n    return createElementObject(circle, extendContext(ctx, {\n        overlayContainer: circle\n    }));\n}, updateCircle);\n"], "mappings": "AAAA,SAASA,mBAAmB,EAAEC,mBAAmB,EAAEC,aAAa,EAAEC,YAAY,QAAQ,qBAAqB;AAC3G,SAASC,MAAM,IAAIC,aAAa,QAAQ,SAAS;AACjD,OAAO,MAAMD,MAAM,GAAGH,mBAAmB,CAAC,SAASK,YAAYA,CAAC;EAAEC,MAAM;EAAGC,QAAQ,EAAEC,EAAE;EAAG,GAAGC;AAAQ,CAAC,EAAEC,GAAG,EAAE;EACzG,MAAMC,MAAM,GAAG,IAAIP,aAAa,CAACE,MAAM,EAAEG,OAAO,CAAC;EACjD,OAAOV,mBAAmB,CAACY,MAAM,EAAEV,aAAa,CAACS,GAAG,EAAE;IAClDE,gBAAgB,EAAED;EACtB,CAAC,CAAC,CAAC;AACP,CAAC,EAAET,YAAY,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}