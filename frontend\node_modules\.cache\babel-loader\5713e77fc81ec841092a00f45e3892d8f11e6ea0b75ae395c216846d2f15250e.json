{"ast": null, "code": "import { createElementObject, createTileLayerComponent, updateGridLayer, withPane } from '@react-leaflet/core';\nimport { TileLayer as LeafletTileLayer } from 'leaflet';\nexport const TileLayer = createTileLayerComponent(function createTileLayer({\n  url,\n  ...options\n}, context) {\n  const layer = new LeafletTileLayer(url, withPane(options, context));\n  return createElementObject(layer, context);\n}, function updateTileLayer(layer, props, prevProps) {\n  updateGridLayer(layer, props, prevProps);\n  const {\n    url\n  } = props;\n  if (url != null && url !== prevProps.url) {\n    layer.setUrl(url);\n  }\n});", "map": {"version": 3, "names": ["createElementObject", "createTileLayerComponent", "updateGridLayer", "with<PERSON>ane", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LeafletTileLayer", "createTileLayer", "url", "options", "context", "layer", "updateTileLayer", "props", "prevProps", "setUrl"], "sources": ["D:/Desktop files/My New Desktop/Irrigation Engineering and Structure Design/frontend/node_modules/react-leaflet/lib/TileLayer.js"], "sourcesContent": ["import { createElementObject, createTileLayerComponent, updateGridLayer, withPane } from '@react-leaflet/core';\nimport { TileLayer as LeafletTileLayer } from 'leaflet';\nexport const TileLayer = createTileLayerComponent(function createTileLayer({ url , ...options }, context) {\n    const layer = new LeafletTileLayer(url, withPane(options, context));\n    return createElementObject(layer, context);\n}, function updateTileLayer(layer, props, prevProps) {\n    updateGridLayer(layer, props, prevProps);\n    const { url  } = props;\n    if (url != null && url !== prevProps.url) {\n        layer.setUrl(url);\n    }\n});\n"], "mappings": "AAAA,SAASA,mBAAmB,EAAEC,wBAAwB,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,qBAAqB;AAC9G,SAASC,SAAS,IAAIC,gBAAgB,QAAQ,SAAS;AACvD,OAAO,MAAMD,SAAS,GAAGH,wBAAwB,CAAC,SAASK,eAAeA,CAAC;EAAEC,GAAG;EAAG,GAAGC;AAAQ,CAAC,EAAEC,OAAO,EAAE;EACtG,MAAMC,KAAK,GAAG,IAAIL,gBAAgB,CAACE,GAAG,EAAEJ,QAAQ,CAACK,OAAO,EAAEC,OAAO,CAAC,CAAC;EACnE,OAAOT,mBAAmB,CAACU,KAAK,EAAED,OAAO,CAAC;AAC9C,CAAC,EAAE,SAASE,eAAeA,CAACD,KAAK,EAAEE,KAAK,EAAEC,SAAS,EAAE;EACjDX,eAAe,CAACQ,KAAK,EAAEE,KAAK,EAAEC,SAAS,CAAC;EACxC,MAAM;IAAEN;EAAK,CAAC,GAAGK,KAAK;EACtB,IAAIL,GAAG,IAAI,IAAI,IAAIA,GAAG,KAAKM,SAAS,CAACN,GAAG,EAAE;IACtCG,KAAK,CAACI,MAAM,CAACP,GAAG,CAAC;EACrB;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}