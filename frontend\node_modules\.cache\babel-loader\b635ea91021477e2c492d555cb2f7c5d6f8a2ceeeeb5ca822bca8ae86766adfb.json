{"ast": null, "code": "import { createElementObject, createPathComponent, extendContext, updateCircle } from '@react-leaflet/core';\nimport { CircleMarker as LeafletCircleMarker } from 'leaflet';\nexport const CircleMarker = createPathComponent(function createCircleMarker({\n  center,\n  children: _c,\n  ...options\n}, ctx) {\n  const marker = new LeafletCircleMarker(center, options);\n  return createElementObject(marker, extendContext(ctx, {\n    overlayContainer: marker\n  }));\n}, updateCircle);", "map": {"version": 3, "names": ["createElementObject", "createPathComponent", "extendContext", "updateCircle", "CircleMarker", "LeafletCircleMarker", "createCircleMarker", "center", "children", "_c", "options", "ctx", "marker", "overlayContainer"], "sources": ["D:/Desktop files/My New Desktop/Irrigation Engineering and Structure Design/frontend/node_modules/react-leaflet/lib/CircleMarker.js"], "sourcesContent": ["import { createElementObject, createPathComponent, extendContext, updateCircle } from '@react-leaflet/core';\nimport { CircleMarker as LeafletCircleMarker } from 'leaflet';\nexport const CircleMarker = createPathComponent(function createCircleMarker({ center , children: _c , ...options }, ctx) {\n    const marker = new LeafletCircleMarker(center, options);\n    return createElementObject(marker, extendContext(ctx, {\n        overlayContainer: marker\n    }));\n}, updateCircle);\n"], "mappings": "AAAA,SAASA,mBAAmB,EAAEC,mBAAmB,EAAEC,aAAa,EAAEC,YAAY,QAAQ,qBAAqB;AAC3G,SAASC,YAAY,IAAIC,mBAAmB,QAAQ,SAAS;AAC7D,OAAO,MAAMD,YAAY,GAAGH,mBAAmB,CAAC,SAASK,kBAAkBA,CAAC;EAAEC,MAAM;EAAGC,QAAQ,EAAEC,EAAE;EAAG,GAAGC;AAAQ,CAAC,EAAEC,GAAG,EAAE;EACrH,MAAMC,MAAM,GAAG,IAAIP,mBAAmB,CAACE,MAAM,EAAEG,OAAO,CAAC;EACvD,OAAOV,mBAAmB,CAACY,MAAM,EAAEV,aAAa,CAACS,GAAG,EAAE;IAClDE,gBAAgB,EAAED;EACtB,CAAC,CAAC,CAAC;AACP,CAAC,EAAET,YAAY,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}