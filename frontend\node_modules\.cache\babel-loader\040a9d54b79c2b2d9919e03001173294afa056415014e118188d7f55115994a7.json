{"ast": null, "code": "import { useEffect, useRef } from 'react';\nimport { useLeafletContext } from './context.js';\nexport function createControlHook(useElement) {\n  return function useLeafletControl(props) {\n    const context = useLeafletContext();\n    const elementRef = useElement(props, context);\n    const {\n      instance\n    } = elementRef.current;\n    const positionRef = useRef(props.position);\n    const {\n      position\n    } = props;\n    useEffect(function addControl() {\n      instance.addTo(context.map);\n      return function removeControl() {\n        instance.remove();\n      };\n    }, [context.map, instance]);\n    useEffect(function updateControl() {\n      if (position != null && position !== positionRef.current) {\n        instance.setPosition(position);\n        positionRef.current = position;\n      }\n    }, [instance, position]);\n    return elementRef;\n  };\n}", "map": {"version": 3, "names": ["useEffect", "useRef", "useLeafletContext", "createControlHook", "useElement", "useLeafletControl", "props", "context", "elementRef", "instance", "current", "positionRef", "position", "addControl", "addTo", "map", "removeControl", "remove", "updateControl", "setPosition"], "sources": ["D:/Desktop files/My New Desktop/Irrigation Engineering and Structure Design/frontend/node_modules/@react-leaflet/core/lib/control.js"], "sourcesContent": ["import { useEffect, useRef } from 'react';\nimport { useLeafletContext } from './context.js';\nexport function createControlHook(useElement) {\n    return function useLeafletControl(props) {\n        const context = useLeafletContext();\n        const elementRef = useElement(props, context);\n        const { instance  } = elementRef.current;\n        const positionRef = useRef(props.position);\n        const { position  } = props;\n        useEffect(function addControl() {\n            instance.addTo(context.map);\n            return function removeControl() {\n                instance.remove();\n            };\n        }, [\n            context.map,\n            instance\n        ]);\n        useEffect(function updateControl() {\n            if (position != null && position !== positionRef.current) {\n                instance.setPosition(position);\n                positionRef.current = position;\n            }\n        }, [\n            instance,\n            position\n        ]);\n        return elementRef;\n    };\n}\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACzC,SAASC,iBAAiB,QAAQ,cAAc;AAChD,OAAO,SAASC,iBAAiBA,CAACC,UAAU,EAAE;EAC1C,OAAO,SAASC,iBAAiBA,CAACC,KAAK,EAAE;IACrC,MAAMC,OAAO,GAAGL,iBAAiB,CAAC,CAAC;IACnC,MAAMM,UAAU,GAAGJ,UAAU,CAACE,KAAK,EAAEC,OAAO,CAAC;IAC7C,MAAM;MAAEE;IAAU,CAAC,GAAGD,UAAU,CAACE,OAAO;IACxC,MAAMC,WAAW,GAAGV,MAAM,CAACK,KAAK,CAACM,QAAQ,CAAC;IAC1C,MAAM;MAAEA;IAAU,CAAC,GAAGN,KAAK;IAC3BN,SAAS,CAAC,SAASa,UAAUA,CAAA,EAAG;MAC5BJ,QAAQ,CAACK,KAAK,CAACP,OAAO,CAACQ,GAAG,CAAC;MAC3B,OAAO,SAASC,aAAaA,CAAA,EAAG;QAC5BP,QAAQ,CAACQ,MAAM,CAAC,CAAC;MACrB,CAAC;IACL,CAAC,EAAE,CACCV,OAAO,CAACQ,GAAG,EACXN,QAAQ,CACX,CAAC;IACFT,SAAS,CAAC,SAASkB,aAAaA,CAAA,EAAG;MAC/B,IAAIN,QAAQ,IAAI,IAAI,IAAIA,QAAQ,KAAKD,WAAW,CAACD,OAAO,EAAE;QACtDD,QAAQ,CAACU,WAAW,CAACP,QAAQ,CAAC;QAC9BD,WAAW,CAACD,OAAO,GAAGE,QAAQ;MAClC;IACJ,CAAC,EAAE,CACCH,QAAQ,EACRG,QAAQ,CACX,CAAC;IACF,OAAOJ,UAAU;EACrB,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}