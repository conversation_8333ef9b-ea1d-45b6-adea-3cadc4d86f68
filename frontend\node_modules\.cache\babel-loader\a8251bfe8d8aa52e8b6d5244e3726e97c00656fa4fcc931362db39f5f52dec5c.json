{"ast": null, "code": "var _jsxFileName = \"D:\\\\Desktop files\\\\My New Desktop\\\\Irrigation Engineering and Structure Design\\\\frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from 'react';\nimport DrawingCanvas from './components/DrawingCanvas';\nimport GridConfig from './components/GridConfig';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [chakBoundary, setChakBoundary] = useState([]);\n  const [chakArea, setChakArea] = useState(0);\n  const [gridConfig, setGridConfig] = useState({\n    murabaSize: 5,\n    killaSize: 1,\n    eastWestUnit: 220,\n    northSouthUnit: 198\n  });\n\n  // Calculate area using Shoelace formula\n  const calculatePolygonArea = points => {\n    if (points.length < 3) return 0;\n    let area = 0;\n    for (let i = 0; i < points.length; i++) {\n      const j = (i + 1) % points.length;\n      area += points[i][0] * points[j][1];\n      area -= points[j][0] * points[i][1];\n    }\n    area = Math.abs(area) / 2;\n\n    // Convert from square degrees to square meters (approximate)\n    const metersPerDegree = 111320; // at equator\n    const areaInSquareMeters = area * Math.pow(metersPerDegree, 2);\n\n    // Convert to acres (1 acre = 4047 square meters)\n    return areaInSquareMeters / 4047;\n  };\n  const handleBoundaryComplete = points => {\n    setChakBoundary(points);\n    const area = calculatePolygonArea(points);\n    setChakArea(area);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Chak Plan Design Tool\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"main-content\",\n      children: [/*#__PURE__*/_jsxDEV(DrawingCanvas, {\n        onBoundaryComplete: handleBoundaryComplete,\n        gridConfig: gridConfig\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(GridConfig, {\n        config: gridConfig,\n        onConfigChange: setGridConfig\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 13\n    }, this), chakBoundary.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"calculation-result\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Chak Plan Summary\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Boundary Points: \", chakBoundary.length]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 43,\n    columnNumber: 9\n  }, this);\n}\n_s(App, \"QDeMyKpDVLyOQAXVaYCLKmpb5WM=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "DrawingCanvas", "GridConfig", "jsxDEV", "_jsxDEV", "App", "_s", "chakBoundary", "setChakBoundary", "chakArea", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gridConfig", "setGridConfig", "murabaSize", "killa<PERSON>ize", "eastWestUnit", "northSouthUnit", "calculatePolygonArea", "points", "length", "area", "i", "j", "Math", "abs", "metersPerDegree", "areaInSquareMeters", "pow", "handleBoundaryComplete", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onBoundaryComplete", "config", "onConfigChange", "_c", "$RefreshReg$"], "sources": ["D:/Desktop files/My New Desktop/Irrigation Engineering and Structure Design/frontend/src/App.js"], "sourcesContent": ["import React, { useState, useRef } from 'react';\r\nimport DrawingCanvas from './components/DrawingCanvas';\r\nimport GridConfig from './components/GridConfig';\r\nimport './App.css';\r\n\r\nfunction App() {\r\n    const [chakBoundary, setChakBoundary] = useState([]);\r\n    const [chakArea, setChakArea] = useState(0);\r\n    const [gridConfig, setGridConfig] = useState({\r\n        murabaSize: 5,\r\n        killaSize: 1,\r\n        eastWestUnit: 220,\r\n        northSouthUnit: 198\r\n    });\r\n\r\n    // Calculate area using Shoelace formula\r\n    const calculatePolygonArea = (points) => {\r\n        if (points.length < 3) return 0;\r\n\r\n        let area = 0;\r\n        for (let i = 0; i < points.length; i++) {\r\n            const j = (i + 1) % points.length;\r\n            area += points[i][0] * points[j][1];\r\n            area -= points[j][0] * points[i][1];\r\n        }\r\n        area = Math.abs(area) / 2;\r\n\r\n        // Convert from square degrees to square meters (approximate)\r\n        const metersPerDegree = 111320; // at equator\r\n        const areaInSquareMeters = area * Math.pow(metersPerDegree, 2);\r\n\r\n        // Convert to acres (1 acre = 4047 square meters)\r\n        return areaInSquareMeters / 4047;\r\n    };\r\n\r\n    const handleBoundaryComplete = (points) => {\r\n        setChakBoundary(points);\r\n        const area = calculatePolygonArea(points);\r\n        setChakArea(area);\r\n    };\r\n\r\n    return (\r\n        <div className=\"app\">\r\n            <h1>Chak Plan Design Tool</h1>\r\n            <div className=\"main-content\">\r\n                <DrawingCanvas\r\n                    onBoundaryComplete={handleBoundaryComplete}\r\n                    gridConfig={gridConfig}\r\n                />\r\n                <GridConfig\r\n                    config={gridConfig}\r\n                    onConfigChange={setGridConfig}\r\n                />\r\n            </div>\r\n            {chakBoundary.length > 0 && (\r\n                <div className=\"calculation-result\">\r\n                    <h3>Chak Plan Summary</h3>\r\n                    <p>Boundary Points: {chakBoundary.length}</p>\r\n                    {/* Area calculation will be added later */}\r\n                </div>\r\n            )}\r\n        </div>\r\n    );\r\n}\r\n\r\nexport default App;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC/C,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACX,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACU,QAAQ,EAAEC,WAAW,CAAC,GAAGX,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACY,UAAU,EAAEC,aAAa,CAAC,GAAGb,QAAQ,CAAC;IACzCc,UAAU,EAAE,CAAC;IACbC,SAAS,EAAE,CAAC;IACZC,YAAY,EAAE,GAAG;IACjBC,cAAc,EAAE;EACpB,CAAC,CAAC;;EAEF;EACA,MAAMC,oBAAoB,GAAIC,MAAM,IAAK;IACrC,IAAIA,MAAM,CAACC,MAAM,GAAG,CAAC,EAAE,OAAO,CAAC;IAE/B,IAAIC,IAAI,GAAG,CAAC;IACZ,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,CAACC,MAAM,EAAEE,CAAC,EAAE,EAAE;MACpC,MAAMC,CAAC,GAAG,CAACD,CAAC,GAAG,CAAC,IAAIH,MAAM,CAACC,MAAM;MACjCC,IAAI,IAAIF,MAAM,CAACG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGH,MAAM,CAACI,CAAC,CAAC,CAAC,CAAC,CAAC;MACnCF,IAAI,IAAIF,MAAM,CAACI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGJ,MAAM,CAACG,CAAC,CAAC,CAAC,CAAC,CAAC;IACvC;IACAD,IAAI,GAAGG,IAAI,CAACC,GAAG,CAACJ,IAAI,CAAC,GAAG,CAAC;;IAEzB;IACA,MAAMK,eAAe,GAAG,MAAM,CAAC,CAAC;IAChC,MAAMC,kBAAkB,GAAGN,IAAI,GAAGG,IAAI,CAACI,GAAG,CAACF,eAAe,EAAE,CAAC,CAAC;;IAE9D;IACA,OAAOC,kBAAkB,GAAG,IAAI;EACpC,CAAC;EAED,MAAME,sBAAsB,GAAIV,MAAM,IAAK;IACvCV,eAAe,CAACU,MAAM,CAAC;IACvB,MAAME,IAAI,GAAGH,oBAAoB,CAACC,MAAM,CAAC;IACzCR,WAAW,CAACU,IAAI,CAAC;EACrB,CAAC;EAED,oBACIhB,OAAA;IAAKyB,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAChB1B,OAAA;MAAA0B,QAAA,EAAI;IAAqB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC9B9B,OAAA;MAAKyB,SAAS,EAAC,cAAc;MAAAC,QAAA,gBACzB1B,OAAA,CAACH,aAAa;QACVkC,kBAAkB,EAAEP,sBAAuB;QAC3CjB,UAAU,EAAEA;MAAW;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eACF9B,OAAA,CAACF,UAAU;QACPkC,MAAM,EAAEzB,UAAW;QACnB0B,cAAc,EAAEzB;MAAc;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,EACL3B,YAAY,CAACY,MAAM,GAAG,CAAC,iBACpBf,OAAA;MAAKyB,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBAC/B1B,OAAA;QAAA0B,QAAA,EAAI;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1B9B,OAAA;QAAA0B,QAAA,GAAG,mBAAiB,EAACvB,YAAY,CAACY,MAAM;MAAA;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAE5C,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd;AAAC5B,EAAA,CA1DQD,GAAG;AAAAiC,EAAA,GAAHjC,GAAG;AA4DZ,eAAeA,GAAG;AAAC,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}