{"name": "@turf/bbox", "version": "6.5.0", "description": "turf bbox module", "author": "Turf Authors", "license": "MIT", "bugs": {"url": "https://github.com/Turfjs/turf/issues"}, "homepage": "https://github.com/Turfjs/turf", "repository": {"type": "git", "url": "git://github.com/Turfjs/turf.git"}, "funding": "https://opencollective.com/turf", "publishConfig": {"access": "public"}, "keywords": ["turf", "extent", "bbox", "polygon", "featurecollection", "g<PERSON><PERSON><PERSON>"], "main": "dist/js/index.js", "module": "dist/es/index.js", "exports": {"./package.json": "./package.json", ".": {"import": "./dist/es/index.js", "require": "./dist/js/index.js"}}, "types": "dist/js/index.d.ts", "sideEffects": false, "files": ["dist"], "scripts": {"bench": "ts-node bench.js", "build": "npm-run-all build:*", "build:es": "tsc --outDir dist/es --module esnext --declaration false && echo '{\"type\":\"module\"}' > dist/es/package.json", "build:js": "tsc", "docs": "node ../../scripts/generate-readmes", "test": "npm-run-all test:*", "test:tape": "ts-node -r esm test.js"}, "devDependencies": {"@types/tape": "*", "benchmark": "*", "npm-run-all": "*", "tape": "*", "ts-node": "*", "tslint": "*", "typescript": "*"}, "dependencies": {"@turf/helpers": "^6.5.0", "@turf/meta": "^6.5.0"}, "gitHead": "5375941072b90d489389db22b43bfe809d5e451e"}