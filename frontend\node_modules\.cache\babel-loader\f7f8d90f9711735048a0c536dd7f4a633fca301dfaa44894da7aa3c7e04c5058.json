{"ast": null, "code": "var _jsxFileName = \"D:\\\\Desktop files\\\\My New Desktop\\\\Irrigation Engineering and Structure Design\\\\frontend\\\\src\\\\components\\\\DrawingCanvas.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { MapContainer, TileLayer, Polygon, Polyline, LayersControl, useMap } from 'react-leaflet';\nimport 'leaflet/dist/leaflet.css';\nimport L from 'leaflet';\nimport 'leaflet-draw/dist/leaflet.draw.css';\nimport 'leaflet-draw';\n\n// Fix for default markers\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\ndelete L.Icon.Default.prototype._getIconUrl;\nL.Icon.Default.mergeOptions({\n  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon-2x.png',\n  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon.png',\n  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-shadow.png'\n});\n\n// Component for drawing controls\nfunction DrawingControls({\n  onBoundaryComplete\n}) {\n  _s();\n  const map = useMap();\n  const drawnItemsRef = useRef(L.featureGroup());\n  useEffect(() => {\n    if (!map) return;\n    const drawnItems = drawnItemsRef.current;\n    map.addLayer(drawnItems);\n\n    // Initialize drawing controls\n    const drawControl = new L.Control.Draw({\n      position: 'topright',\n      draw: {\n        polygon: {\n          shapeOptions: {\n            color: '#3388ff',\n            fillOpacity: 0.2,\n            weight: 2\n          },\n          allowIntersection: false,\n          showArea: true\n        },\n        polyline: {\n          shapeOptions: {\n            color: '#ff7800',\n            weight: 3\n          }\n        },\n        rectangle: {\n          shapeOptions: {\n            color: '#ff7800',\n            fillOpacity: 0.1\n          }\n        },\n        circle: false,\n        circlemarker: false,\n        marker: true\n      },\n      edit: {\n        featureGroup: drawnItems,\n        remove: true\n      }\n    });\n    map.addControl(drawControl);\n\n    // Handle drawing events\n    const handleDrawCreated = e => {\n      const layer = e.layer;\n      drawnItems.addLayer(layer);\n      if (e.layerType === 'polygon') {\n        const points = layer.getLatLngs()[0].map(point => [point.lat, point.lng]);\n        onBoundaryComplete(points);\n      }\n    };\n    map.on(L.Draw.Event.CREATED, handleDrawCreated);\n    return () => {\n      map.removeControl(drawControl);\n      map.removeLayer(drawnItems);\n      map.off(L.Draw.Event.CREATED, handleDrawCreated);\n    };\n  }, [map, onBoundaryComplete]);\n  return null;\n}\n\n// Component for grid rendering\n_s(DrawingControls, \"EQFLR9AsIoDy5V2W9qZoahcFQ9Y=\", false, function () {\n  return [useMap];\n});\n_c = DrawingControls;\nfunction GridRenderer({\n  polygonPoints,\n  gridConfig\n}) {\n  _s2();\n  const map = useMap();\n  const gridLayerRef = useRef(null);\n  useEffect(() => {\n    if (!map || polygonPoints.length === 0) return;\n\n    // Remove existing grid layer\n    if (gridLayerRef.current) {\n      map.removeLayer(gridLayerRef.current);\n    }\n\n    // Calculate grid based on polygon and config\n    const gridLayer = L.layerGroup();\n\n    // Get polygon bounds\n    const polygon = L.polygon(polygonPoints);\n    const bounds = polygon.getBounds();\n\n    // Convert feet to meters (approximate)\n    const eastWestMeters = gridConfig.eastWestUnit * 0.3048;\n    const northSouthMeters = gridConfig.northSouthUnit * 0.3048;\n\n    // Calculate grid lines\n    const latStep = northSouthMeters / 111320; // Approximate meters to degrees\n    const lngStep = eastWestMeters / (111320 * Math.cos(bounds.getCenter().lat * Math.PI / 180));\n\n    // Draw vertical lines (East-West)\n    for (let lng = bounds.getWest(); lng <= bounds.getEast(); lng += lngStep) {\n      const line = L.polyline([[bounds.getNorth(), lng], [bounds.getSouth(), lng]], {\n        color: '#666',\n        weight: 1,\n        opacity: 0.5\n      });\n      gridLayer.addLayer(line);\n    }\n\n    // Draw horizontal lines (North-South)\n    for (let lat = bounds.getSouth(); lat <= bounds.getNorth(); lat += latStep) {\n      const line = L.polyline([[lat, bounds.getWest()], [lat, bounds.getEast()]], {\n        color: '#666',\n        weight: 1,\n        opacity: 0.5\n      });\n      gridLayer.addLayer(line);\n    }\n    gridLayerRef.current = gridLayer;\n    map.addLayer(gridLayer);\n    return () => {\n      if (gridLayerRef.current) {\n        map.removeLayer(gridLayerRef.current);\n      }\n    };\n  }, [map, polygonPoints, gridConfig]);\n  return null;\n}\n_s2(GridRenderer, \"Xyfvx4tNxPHGzhrfcRdGCSWol4g=\", false, function () {\n  return [useMap];\n});\n_c2 = GridRenderer;\nfunction DrawingCanvas({\n  onBoundaryComplete,\n  gridConfig\n}) {\n  _s3();\n  const [polygonPoints, setPolygonPoints] = useState([]);\n  const [canalData, setCanalData] = useState([]);\n\n  // Sample canal data for demonstration\n  useEffect(() => {\n    // This would typically come from a GIS database or API\n    const sampleCanals = [{\n      id: 1,\n      name: \"Main Canal\",\n      coordinates: [[28.6139, 77.2090], [28.6150, 77.2100], [28.6160, 77.2110], [28.6170, 77.2120]],\n      type: \"main\"\n    }, {\n      id: 2,\n      name: \"Branch Canal A\",\n      coordinates: [[28.6150, 77.2100], [28.6140, 77.2105], [28.6130, 77.2110]],\n      type: \"branch\"\n    }, {\n      id: 3,\n      name: \"Distributary 1\",\n      coordinates: [[28.6140, 77.2105], [28.6135, 77.2095], [28.6130, 77.2085]],\n      type: \"distributary\"\n    }];\n    setCanalData(sampleCanals);\n  }, []);\n  const handleBoundaryComplete = points => {\n    setPolygonPoints(points);\n    onBoundaryComplete(points);\n  };\n  const getCanalColor = type => {\n    switch (type) {\n      case 'main':\n        return '#0066cc';\n      case 'branch':\n        return '#0099ff';\n      case 'distributary':\n        return '#33ccff';\n      default:\n        return '#0066cc';\n    }\n  };\n  const getCanalWeight = type => {\n    switch (type) {\n      case 'main':\n        return 6;\n      case 'branch':\n        return 4;\n      case 'distributary':\n        return 2;\n      default:\n        return 3;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"drawing-canvas\",\n    children: /*#__PURE__*/_jsxDEV(MapContainer, {\n      center: [28.6139, 77.2090] // Default to New Delhi coordinates\n      ,\n      zoom: 15,\n      style: {\n        height: '600px',\n        width: '100%'\n      },\n      ref: mapRef,\n      children: [/*#__PURE__*/_jsxDEV(LayersControl, {\n        position: \"topright\",\n        children: [/*#__PURE__*/_jsxDEV(LayersControl.BaseLayer, {\n          checked: true,\n          name: \"OpenStreetMap\",\n          children: /*#__PURE__*/_jsxDEV(TileLayer, {\n            url: \"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\",\n            attribution: \"\\xA9 <a href=\\\"https://www.openstreetmap.org/copyright\\\">OpenStreetMap</a> contributors\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(LayersControl.BaseLayer, {\n          name: \"Satellite\",\n          children: /*#__PURE__*/_jsxDEV(TileLayer, {\n            url: \"https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}\",\n            attribution: \"\\xA9 <a href=\\\"https://www.esri.com/\\\">Esri</a>\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(LayersControl.BaseLayer, {\n          name: \"Terrain\",\n          children: /*#__PURE__*/_jsxDEV(TileLayer, {\n            url: \"https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png\",\n            attribution: \"\\xA9 <a href=\\\"https://opentopomap.org/\\\">OpenTopoMap</a>\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(LayersControl.Overlay, {\n          checked: true,\n          name: \"Canal Network\",\n          children: /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: canalData.map(canal => /*#__PURE__*/_jsxDEV(Polyline, {\n              positions: canal.coordinates,\n              color: getCanalColor(canal.type),\n              weight: getCanalWeight(canal.type),\n              opacity: 0.8\n            }, canal.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 17\n            }, this))\n          }, void 0, false)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(LayersControl.Overlay, {\n          name: \"Chak Boundary\",\n          children: /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: polygonPoints.length > 0 && /*#__PURE__*/_jsxDEV(Polygon, {\n              positions: polygonPoints,\n              color: \"#3388ff\",\n              fillOpacity: 0.2,\n              weight: 3\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 17\n            }, this)\n          }, void 0, false)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DrawingControls, {\n        onBoundaryComplete: handleBoundaryComplete\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(GridRenderer, {\n        polygonPoints: polygonPoints,\n        gridConfig: gridConfig\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 221,\n    columnNumber: 5\n  }, this);\n}\n_s3(DrawingCanvas, \"1+R0u2yW4IAHJDDE8mswDg9jaoI=\");\n_c3 = DrawingCanvas;\nexport default DrawingCanvas;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"DrawingControls\");\n$RefreshReg$(_c2, \"GridRenderer\");\n$RefreshReg$(_c3, \"DrawingCanvas\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "MapContainer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Polygon", "Polyline", "LayersControl", "useMap", "L", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Icon", "<PERSON><PERSON><PERSON>", "prototype", "_getIconUrl", "mergeOptions", "iconRetinaUrl", "iconUrl", "shadowUrl", "DrawingControls", "onBoundaryComplete", "_s", "map", "drawnItemsRef", "featureGroup", "drawnItems", "current", "add<PERSON><PERSON>er", "drawControl", "Control", "Draw", "position", "draw", "polygon", "shapeOptions", "color", "fillOpacity", "weight", "allowIntersection", "showArea", "polyline", "rectangle", "circle", "circlemarker", "marker", "edit", "remove", "addControl", "handleDrawCreated", "e", "layer", "layerType", "points", "getLatLngs", "point", "lat", "lng", "on", "Event", "CREATED", "removeControl", "<PERSON><PERSON><PERSON>er", "off", "_c", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "polygonPoints", "gridConfig", "_s2", "gridLayerRef", "length", "gridLayer", "layerGroup", "bounds", "getBounds", "eastWestMeters", "eastWestUnit", "northSouthMeters", "northSouthUnit", "latStep", "lngStep", "Math", "cos", "getCenter", "PI", "getWest", "getEast", "line", "getNorth", "getSouth", "opacity", "_c2", "DrawingCanvas", "_s3", "setPolygonPoints", "canalData", "setCanalData", "sampleCanals", "id", "name", "coordinates", "type", "handleBoundaryComplete", "getCanalColor", "getCanalWeight", "className", "children", "center", "zoom", "style", "height", "width", "ref", "mapRef", "Base<PERSON><PERSON>er", "checked", "url", "attribution", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Overlay", "canal", "positions", "_c3", "$RefreshReg$"], "sources": ["D:/Desktop files/My New Desktop/Irrigation Engineering and Structure Design/frontend/src/components/DrawingCanvas.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\r\nimport { Map<PERSON>ontainer, TileLayer, Polygon, Polyline, LayersControl, useMap } from 'react-leaflet';\r\nimport 'leaflet/dist/leaflet.css';\r\nimport L from 'leaflet';\r\nimport 'leaflet-draw/dist/leaflet.draw.css';\r\nimport 'leaflet-draw';\r\n\r\n// Fix for default markers\r\ndelete L.Icon.Default.prototype._getIconUrl;\r\nL.Icon.Default.mergeOptions({\r\n  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon-2x.png',\r\n  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon.png',\r\n  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-shadow.png',\r\n});\r\n\r\n// Component for drawing controls\r\nfunction DrawingControls({ onBoundaryComplete }) {\r\n  const map = useMap();\r\n  const drawnItemsRef = useRef(L.featureGroup());\r\n\r\n  useEffect(() => {\r\n    if (!map) return;\r\n\r\n    const drawnItems = drawnItemsRef.current;\r\n    map.addLayer(drawnItems);\r\n\r\n    // Initialize drawing controls\r\n    const drawControl = new L.Control.Draw({\r\n      position: 'topright',\r\n      draw: {\r\n        polygon: {\r\n          shapeOptions: {\r\n            color: '#3388ff',\r\n            fillOpacity: 0.2,\r\n            weight: 2\r\n          },\r\n          allowIntersection: false,\r\n          showArea: true\r\n        },\r\n        polyline: {\r\n          shapeOptions: {\r\n            color: '#ff7800',\r\n            weight: 3\r\n          }\r\n        },\r\n        rectangle: {\r\n          shapeOptions: {\r\n            color: '#ff7800',\r\n            fillOpacity: 0.1\r\n          }\r\n        },\r\n        circle: false,\r\n        circlemarker: false,\r\n        marker: true\r\n      },\r\n      edit: {\r\n        featureGroup: drawnItems,\r\n        remove: true\r\n      }\r\n    });\r\n\r\n    map.addControl(drawControl);\r\n\r\n    // Handle drawing events\r\n    const handleDrawCreated = (e) => {\r\n      const layer = e.layer;\r\n      drawnItems.addLayer(layer);\r\n\r\n      if (e.layerType === 'polygon') {\r\n        const points = layer.getLatLngs()[0].map(point => [point.lat, point.lng]);\r\n        onBoundaryComplete(points);\r\n      }\r\n    };\r\n\r\n    map.on(L.Draw.Event.CREATED, handleDrawCreated);\r\n\r\n    return () => {\r\n      map.removeControl(drawControl);\r\n      map.removeLayer(drawnItems);\r\n      map.off(L.Draw.Event.CREATED, handleDrawCreated);\r\n    };\r\n  }, [map, onBoundaryComplete]);\r\n\r\n  return null;\r\n}\r\n\r\n// Component for grid rendering\r\nfunction GridRenderer({ polygonPoints, gridConfig }) {\r\n  const map = useMap();\r\n  const gridLayerRef = useRef(null);\r\n\r\n  useEffect(() => {\r\n    if (!map || polygonPoints.length === 0) return;\r\n\r\n    // Remove existing grid layer\r\n    if (gridLayerRef.current) {\r\n      map.removeLayer(gridLayerRef.current);\r\n    }\r\n\r\n    // Calculate grid based on polygon and config\r\n    const gridLayer = L.layerGroup();\r\n\r\n    // Get polygon bounds\r\n    const polygon = L.polygon(polygonPoints);\r\n    const bounds = polygon.getBounds();\r\n\r\n    // Convert feet to meters (approximate)\r\n    const eastWestMeters = gridConfig.eastWestUnit * 0.3048;\r\n    const northSouthMeters = gridConfig.northSouthUnit * 0.3048;\r\n\r\n    // Calculate grid lines\r\n    const latStep = northSouthMeters / 111320; // Approximate meters to degrees\r\n    const lngStep = eastWestMeters / (111320 * Math.cos(bounds.getCenter().lat * Math.PI / 180));\r\n\r\n    // Draw vertical lines (East-West)\r\n    for (let lng = bounds.getWest(); lng <= bounds.getEast(); lng += lngStep) {\r\n      const line = L.polyline([\r\n        [bounds.getNorth(), lng],\r\n        [bounds.getSouth(), lng]\r\n      ], {\r\n        color: '#666',\r\n        weight: 1,\r\n        opacity: 0.5\r\n      });\r\n      gridLayer.addLayer(line);\r\n    }\r\n\r\n    // Draw horizontal lines (North-South)\r\n    for (let lat = bounds.getSouth(); lat <= bounds.getNorth(); lat += latStep) {\r\n      const line = L.polyline([\r\n        [lat, bounds.getWest()],\r\n        [lat, bounds.getEast()]\r\n      ], {\r\n        color: '#666',\r\n        weight: 1,\r\n        opacity: 0.5\r\n      });\r\n      gridLayer.addLayer(line);\r\n    }\r\n\r\n    gridLayerRef.current = gridLayer;\r\n    map.addLayer(gridLayer);\r\n\r\n    return () => {\r\n      if (gridLayerRef.current) {\r\n        map.removeLayer(gridLayerRef.current);\r\n      }\r\n    };\r\n  }, [map, polygonPoints, gridConfig]);\r\n\r\n  return null;\r\n}\r\n\r\nfunction DrawingCanvas({ onBoundaryComplete, gridConfig }) {\r\n  const [polygonPoints, setPolygonPoints] = useState([]);\r\n  const [canalData, setCanalData] = useState([]);\r\n\r\n  // Sample canal data for demonstration\r\n  useEffect(() => {\r\n    // This would typically come from a GIS database or API\r\n    const sampleCanals = [\r\n      {\r\n        id: 1,\r\n        name: \"Main Canal\",\r\n        coordinates: [\r\n          [28.6139, 77.2090],\r\n          [28.6150, 77.2100],\r\n          [28.6160, 77.2110],\r\n          [28.6170, 77.2120]\r\n        ],\r\n        type: \"main\"\r\n      },\r\n      {\r\n        id: 2,\r\n        name: \"Branch Canal A\",\r\n        coordinates: [\r\n          [28.6150, 77.2100],\r\n          [28.6140, 77.2105],\r\n          [28.6130, 77.2110]\r\n        ],\r\n        type: \"branch\"\r\n      },\r\n      {\r\n        id: 3,\r\n        name: \"Distributary 1\",\r\n        coordinates: [\r\n          [28.6140, 77.2105],\r\n          [28.6135, 77.2095],\r\n          [28.6130, 77.2085]\r\n        ],\r\n        type: \"distributary\"\r\n      }\r\n    ];\r\n    setCanalData(sampleCanals);\r\n  }, []);\r\n\r\n  const handleBoundaryComplete = (points) => {\r\n    setPolygonPoints(points);\r\n    onBoundaryComplete(points);\r\n  };\r\n\r\n  const getCanalColor = (type) => {\r\n    switch (type) {\r\n      case 'main': return '#0066cc';\r\n      case 'branch': return '#0099ff';\r\n      case 'distributary': return '#33ccff';\r\n      default: return '#0066cc';\r\n    }\r\n  };\r\n\r\n  const getCanalWeight = (type) => {\r\n    switch (type) {\r\n      case 'main': return 6;\r\n      case 'branch': return 4;\r\n      case 'distributary': return 2;\r\n      default: return 3;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"drawing-canvas\">\r\n      <MapContainer\r\n        center={[28.6139, 77.2090]}  // Default to New Delhi coordinates\r\n        zoom={15}\r\n        style={{ height: '600px', width: '100%' }}\r\n        ref={mapRef}\r\n      >\r\n        <LayersControl position=\"topright\">\r\n          <LayersControl.BaseLayer checked name=\"OpenStreetMap\">\r\n            <TileLayer\r\n              url=\"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\"\r\n              attribution='&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors'\r\n            />\r\n          </LayersControl.BaseLayer>\r\n\r\n          <LayersControl.BaseLayer name=\"Satellite\">\r\n            <TileLayer\r\n              url=\"https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}\"\r\n              attribution='&copy; <a href=\"https://www.esri.com/\">Esri</a>'\r\n            />\r\n          </LayersControl.BaseLayer>\r\n\r\n          <LayersControl.BaseLayer name=\"Terrain\">\r\n            <TileLayer\r\n              url=\"https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png\"\r\n              attribution='&copy; <a href=\"https://opentopomap.org/\">OpenTopoMap</a>'\r\n            />\r\n          </LayersControl.BaseLayer>\r\n\r\n          <LayersControl.Overlay checked name=\"Canal Network\">\r\n            <>\r\n              {canalData.map((canal) => (\r\n                <Polyline\r\n                  key={canal.id}\r\n                  positions={canal.coordinates}\r\n                  color={getCanalColor(canal.type)}\r\n                  weight={getCanalWeight(canal.type)}\r\n                  opacity={0.8}\r\n                />\r\n              ))}\r\n            </>\r\n          </LayersControl.Overlay>\r\n\r\n          <LayersControl.Overlay name=\"Chak Boundary\">\r\n            <>\r\n              {polygonPoints.length > 0 && (\r\n                <Polygon\r\n                  positions={polygonPoints}\r\n                  color=\"#3388ff\"\r\n                  fillOpacity={0.2}\r\n                  weight={3}\r\n                />\r\n              )}\r\n            </>\r\n          </LayersControl.Overlay>\r\n        </LayersControl>\r\n\r\n        {/* Drawing Controls */}\r\n        <DrawingControls onBoundaryComplete={handleBoundaryComplete} />\r\n\r\n        {/* Grid Renderer */}\r\n        <GridRenderer polygonPoints={polygonPoints} gridConfig={gridConfig} />\r\n      </MapContainer>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default DrawingCanvas;"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,YAAY,EAAEC,SAAS,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,MAAM,QAAQ,eAAe;AACjG,OAAO,0BAA0B;AACjC,OAAOC,CAAC,MAAM,SAAS;AACvB,OAAO,oCAAoC;AAC3C,OAAO,cAAc;;AAErB;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,OAAOJ,CAAC,CAACK,IAAI,CAACC,OAAO,CAACC,SAAS,CAACC,WAAW;AAC3CR,CAAC,CAACK,IAAI,CAACC,OAAO,CAACG,YAAY,CAAC;EAC1BC,aAAa,EAAE,gFAAgF;EAC/FC,OAAO,EAAE,6EAA6E;EACtFC,SAAS,EAAE;AACb,CAAC,CAAC;;AAEF;AACA,SAASC,eAAeA,CAAC;EAAEC;AAAmB,CAAC,EAAE;EAAAC,EAAA;EAC/C,MAAMC,GAAG,GAAGjB,MAAM,CAAC,CAAC;EACpB,MAAMkB,aAAa,GAAGxB,MAAM,CAACO,CAAC,CAACkB,YAAY,CAAC,CAAC,CAAC;EAE9C1B,SAAS,CAAC,MAAM;IACd,IAAI,CAACwB,GAAG,EAAE;IAEV,MAAMG,UAAU,GAAGF,aAAa,CAACG,OAAO;IACxCJ,GAAG,CAACK,QAAQ,CAACF,UAAU,CAAC;;IAExB;IACA,MAAMG,WAAW,GAAG,IAAItB,CAAC,CAACuB,OAAO,CAACC,IAAI,CAAC;MACrCC,QAAQ,EAAE,UAAU;MACpBC,IAAI,EAAE;QACJC,OAAO,EAAE;UACPC,YAAY,EAAE;YACZC,KAAK,EAAE,SAAS;YAChBC,WAAW,EAAE,GAAG;YAChBC,MAAM,EAAE;UACV,CAAC;UACDC,iBAAiB,EAAE,KAAK;UACxBC,QAAQ,EAAE;QACZ,CAAC;QACDC,QAAQ,EAAE;UACRN,YAAY,EAAE;YACZC,KAAK,EAAE,SAAS;YAChBE,MAAM,EAAE;UACV;QACF,CAAC;QACDI,SAAS,EAAE;UACTP,YAAY,EAAE;YACZC,KAAK,EAAE,SAAS;YAChBC,WAAW,EAAE;UACf;QACF,CAAC;QACDM,MAAM,EAAE,KAAK;QACbC,YAAY,EAAE,KAAK;QACnBC,MAAM,EAAE;MACV,CAAC;MACDC,IAAI,EAAE;QACJrB,YAAY,EAAEC,UAAU;QACxBqB,MAAM,EAAE;MACV;IACF,CAAC,CAAC;IAEFxB,GAAG,CAACyB,UAAU,CAACnB,WAAW,CAAC;;IAE3B;IACA,MAAMoB,iBAAiB,GAAIC,CAAC,IAAK;MAC/B,MAAMC,KAAK,GAAGD,CAAC,CAACC,KAAK;MACrBzB,UAAU,CAACE,QAAQ,CAACuB,KAAK,CAAC;MAE1B,IAAID,CAAC,CAACE,SAAS,KAAK,SAAS,EAAE;QAC7B,MAAMC,MAAM,GAAGF,KAAK,CAACG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC/B,GAAG,CAACgC,KAAK,IAAI,CAACA,KAAK,CAACC,GAAG,EAAED,KAAK,CAACE,GAAG,CAAC,CAAC;QACzEpC,kBAAkB,CAACgC,MAAM,CAAC;MAC5B;IACF,CAAC;IAED9B,GAAG,CAACmC,EAAE,CAACnD,CAAC,CAACwB,IAAI,CAAC4B,KAAK,CAACC,OAAO,EAAEX,iBAAiB,CAAC;IAE/C,OAAO,MAAM;MACX1B,GAAG,CAACsC,aAAa,CAAChC,WAAW,CAAC;MAC9BN,GAAG,CAACuC,WAAW,CAACpC,UAAU,CAAC;MAC3BH,GAAG,CAACwC,GAAG,CAACxD,CAAC,CAACwB,IAAI,CAAC4B,KAAK,CAACC,OAAO,EAAEX,iBAAiB,CAAC;IAClD,CAAC;EACH,CAAC,EAAE,CAAC1B,GAAG,EAAEF,kBAAkB,CAAC,CAAC;EAE7B,OAAO,IAAI;AACb;;AAEA;AAAAC,EAAA,CAtESF,eAAe;EAAA,QACVd,MAAM;AAAA;AAAA0D,EAAA,GADX5C,eAAe;AAuExB,SAAS6C,YAAYA,CAAC;EAAEC,aAAa;EAAEC;AAAW,CAAC,EAAE;EAAAC,GAAA;EACnD,MAAM7C,GAAG,GAAGjB,MAAM,CAAC,CAAC;EACpB,MAAM+D,YAAY,GAAGrE,MAAM,CAAC,IAAI,CAAC;EAEjCD,SAAS,CAAC,MAAM;IACd,IAAI,CAACwB,GAAG,IAAI2C,aAAa,CAACI,MAAM,KAAK,CAAC,EAAE;;IAExC;IACA,IAAID,YAAY,CAAC1C,OAAO,EAAE;MACxBJ,GAAG,CAACuC,WAAW,CAACO,YAAY,CAAC1C,OAAO,CAAC;IACvC;;IAEA;IACA,MAAM4C,SAAS,GAAGhE,CAAC,CAACiE,UAAU,CAAC,CAAC;;IAEhC;IACA,MAAMtC,OAAO,GAAG3B,CAAC,CAAC2B,OAAO,CAACgC,aAAa,CAAC;IACxC,MAAMO,MAAM,GAAGvC,OAAO,CAACwC,SAAS,CAAC,CAAC;;IAElC;IACA,MAAMC,cAAc,GAAGR,UAAU,CAACS,YAAY,GAAG,MAAM;IACvD,MAAMC,gBAAgB,GAAGV,UAAU,CAACW,cAAc,GAAG,MAAM;;IAE3D;IACA,MAAMC,OAAO,GAAGF,gBAAgB,GAAG,MAAM,CAAC,CAAC;IAC3C,MAAMG,OAAO,GAAGL,cAAc,IAAI,MAAM,GAAGM,IAAI,CAACC,GAAG,CAACT,MAAM,CAACU,SAAS,CAAC,CAAC,CAAC3B,GAAG,GAAGyB,IAAI,CAACG,EAAE,GAAG,GAAG,CAAC,CAAC;;IAE5F;IACA,KAAK,IAAI3B,GAAG,GAAGgB,MAAM,CAACY,OAAO,CAAC,CAAC,EAAE5B,GAAG,IAAIgB,MAAM,CAACa,OAAO,CAAC,CAAC,EAAE7B,GAAG,IAAIuB,OAAO,EAAE;MACxE,MAAMO,IAAI,GAAGhF,CAAC,CAACkC,QAAQ,CAAC,CACtB,CAACgC,MAAM,CAACe,QAAQ,CAAC,CAAC,EAAE/B,GAAG,CAAC,EACxB,CAACgB,MAAM,CAACgB,QAAQ,CAAC,CAAC,EAAEhC,GAAG,CAAC,CACzB,EAAE;QACDrB,KAAK,EAAE,MAAM;QACbE,MAAM,EAAE,CAAC;QACToD,OAAO,EAAE;MACX,CAAC,CAAC;MACFnB,SAAS,CAAC3C,QAAQ,CAAC2D,IAAI,CAAC;IAC1B;;IAEA;IACA,KAAK,IAAI/B,GAAG,GAAGiB,MAAM,CAACgB,QAAQ,CAAC,CAAC,EAAEjC,GAAG,IAAIiB,MAAM,CAACe,QAAQ,CAAC,CAAC,EAAEhC,GAAG,IAAIuB,OAAO,EAAE;MAC1E,MAAMQ,IAAI,GAAGhF,CAAC,CAACkC,QAAQ,CAAC,CACtB,CAACe,GAAG,EAAEiB,MAAM,CAACY,OAAO,CAAC,CAAC,CAAC,EACvB,CAAC7B,GAAG,EAAEiB,MAAM,CAACa,OAAO,CAAC,CAAC,CAAC,CACxB,EAAE;QACDlD,KAAK,EAAE,MAAM;QACbE,MAAM,EAAE,CAAC;QACToD,OAAO,EAAE;MACX,CAAC,CAAC;MACFnB,SAAS,CAAC3C,QAAQ,CAAC2D,IAAI,CAAC;IAC1B;IAEAlB,YAAY,CAAC1C,OAAO,GAAG4C,SAAS;IAChChD,GAAG,CAACK,QAAQ,CAAC2C,SAAS,CAAC;IAEvB,OAAO,MAAM;MACX,IAAIF,YAAY,CAAC1C,OAAO,EAAE;QACxBJ,GAAG,CAACuC,WAAW,CAACO,YAAY,CAAC1C,OAAO,CAAC;MACvC;IACF,CAAC;EACH,CAAC,EAAE,CAACJ,GAAG,EAAE2C,aAAa,EAAEC,UAAU,CAAC,CAAC;EAEpC,OAAO,IAAI;AACb;AAACC,GAAA,CAhEQH,YAAY;EAAA,QACP3D,MAAM;AAAA;AAAAqF,GAAA,GADX1B,YAAY;AAkErB,SAAS2B,aAAaA,CAAC;EAAEvE,kBAAkB;EAAE8C;AAAW,CAAC,EAAE;EAAA0B,GAAA;EACzD,MAAM,CAAC3B,aAAa,EAAE4B,gBAAgB,CAAC,GAAGhG,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACiG,SAAS,EAAEC,YAAY,CAAC,GAAGlG,QAAQ,CAAC,EAAE,CAAC;;EAE9C;EACAC,SAAS,CAAC,MAAM;IACd;IACA,MAAMkG,YAAY,GAAG,CACnB;MACEC,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,YAAY;MAClBC,WAAW,EAAE,CACX,CAAC,OAAO,EAAE,OAAO,CAAC,EAClB,CAAC,OAAO,EAAE,OAAO,CAAC,EAClB,CAAC,OAAO,EAAE,OAAO,CAAC,EAClB,CAAC,OAAO,EAAE,OAAO,CAAC,CACnB;MACDC,IAAI,EAAE;IACR,CAAC,EACD;MACEH,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,gBAAgB;MACtBC,WAAW,EAAE,CACX,CAAC,OAAO,EAAE,OAAO,CAAC,EAClB,CAAC,OAAO,EAAE,OAAO,CAAC,EAClB,CAAC,OAAO,EAAE,OAAO,CAAC,CACnB;MACDC,IAAI,EAAE;IACR,CAAC,EACD;MACEH,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,gBAAgB;MACtBC,WAAW,EAAE,CACX,CAAC,OAAO,EAAE,OAAO,CAAC,EAClB,CAAC,OAAO,EAAE,OAAO,CAAC,EAClB,CAAC,OAAO,EAAE,OAAO,CAAC,CACnB;MACDC,IAAI,EAAE;IACR,CAAC,CACF;IACDL,YAAY,CAACC,YAAY,CAAC;EAC5B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,sBAAsB,GAAIjD,MAAM,IAAK;IACzCyC,gBAAgB,CAACzC,MAAM,CAAC;IACxBhC,kBAAkB,CAACgC,MAAM,CAAC;EAC5B,CAAC;EAED,MAAMkD,aAAa,GAAIF,IAAI,IAAK;IAC9B,QAAQA,IAAI;MACV,KAAK,MAAM;QAAE,OAAO,SAAS;MAC7B,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,cAAc;QAAE,OAAO,SAAS;MACrC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMG,cAAc,GAAIH,IAAI,IAAK;IAC/B,QAAQA,IAAI;MACV,KAAK,MAAM;QAAE,OAAO,CAAC;MACrB,KAAK,QAAQ;QAAE,OAAO,CAAC;MACvB,KAAK,cAAc;QAAE,OAAO,CAAC;MAC7B;QAAS,OAAO,CAAC;IACnB;EACF,CAAC;EAED,oBACE5F,OAAA;IAAKgG,SAAS,EAAC,gBAAgB;IAAAC,QAAA,eAC7BjG,OAAA,CAACR,YAAY;MACX0G,MAAM,EAAE,CAAC,OAAO,EAAE,OAAO,CAAE,CAAE;MAAA;MAC7BC,IAAI,EAAE,EAAG;MACTC,KAAK,EAAE;QAAEC,MAAM,EAAE,OAAO;QAAEC,KAAK,EAAE;MAAO,CAAE;MAC1CC,GAAG,EAAEC,MAAO;MAAAP,QAAA,gBAEZjG,OAAA,CAACJ,aAAa;QAAC2B,QAAQ,EAAC,UAAU;QAAA0E,QAAA,gBAChCjG,OAAA,CAACJ,aAAa,CAAC6G,SAAS;UAACC,OAAO;UAAChB,IAAI,EAAC,eAAe;UAAAO,QAAA,eACnDjG,OAAA,CAACP,SAAS;YACRkH,GAAG,EAAC,oDAAoD;YACxDC,WAAW,EAAC;UAAyF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACqB,CAAC,eAE1BhH,OAAA,CAACJ,aAAa,CAAC6G,SAAS;UAACf,IAAI,EAAC,WAAW;UAAAO,QAAA,eACvCjG,OAAA,CAACP,SAAS;YACRkH,GAAG,EAAC,+FAA+F;YACnGC,WAAW,EAAC;UAAiD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACqB,CAAC,eAE1BhH,OAAA,CAACJ,aAAa,CAAC6G,SAAS;UAACf,IAAI,EAAC,SAAS;UAAAO,QAAA,eACrCjG,OAAA,CAACP,SAAS;YACRkH,GAAG,EAAC,kDAAkD;YACtDC,WAAW,EAAC;UAA2D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACqB,CAAC,eAE1BhH,OAAA,CAACJ,aAAa,CAACqH,OAAO;UAACP,OAAO;UAAChB,IAAI,EAAC,eAAe;UAAAO,QAAA,eACjDjG,OAAA,CAAAE,SAAA;YAAA+F,QAAA,EACGX,SAAS,CAACxE,GAAG,CAAEoG,KAAK,iBACnBlH,OAAA,CAACL,QAAQ;cAEPwH,SAAS,EAAED,KAAK,CAACvB,WAAY;cAC7BhE,KAAK,EAAEmE,aAAa,CAACoB,KAAK,CAACtB,IAAI,CAAE;cACjC/D,MAAM,EAAEkE,cAAc,CAACmB,KAAK,CAACtB,IAAI,CAAE;cACnCX,OAAO,EAAE;YAAI,GAJRiC,KAAK,CAACzB,EAAE;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKd,CACF;UAAC,gBACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACkB,CAAC,eAExBhH,OAAA,CAACJ,aAAa,CAACqH,OAAO;UAACvB,IAAI,EAAC,eAAe;UAAAO,QAAA,eACzCjG,OAAA,CAAAE,SAAA;YAAA+F,QAAA,EACGxC,aAAa,CAACI,MAAM,GAAG,CAAC,iBACvB7D,OAAA,CAACN,OAAO;cACNyH,SAAS,EAAE1D,aAAc;cACzB9B,KAAK,EAAC,SAAS;cACfC,WAAW,EAAE,GAAI;cACjBC,MAAM,EAAE;YAAE;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX;UACF,gBACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACkB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC,eAGhBhH,OAAA,CAACW,eAAe;QAACC,kBAAkB,EAAEiF;MAAuB;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAG/DhH,OAAA,CAACwD,YAAY;QAACC,aAAa,EAAEA,aAAc;QAACC,UAAU,EAAEA;MAAW;QAAAmD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1D;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEV;AAAC5B,GAAA,CApIQD,aAAa;AAAAiC,GAAA,GAAbjC,aAAa;AAsItB,eAAeA,aAAa;AAAC,IAAA5B,EAAA,EAAA2B,GAAA,EAAAkC,GAAA;AAAAC,YAAA,CAAA9D,EAAA;AAAA8D,YAAA,CAAAnC,GAAA;AAAAmC,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}