.app-footer {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: white;
  margin-top: 50px;
  padding: 40px 0 20px 0;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.footer-section h4 {
  margin: 0 0 15px 0;
  font-size: 1.1rem;
  color: #ecf0f1;
  border-bottom: 2px solid #3498db;
  padding-bottom: 8px;
}

.footer-section p {
  margin: 0 0 10px 0;
  line-height: 1.6;
  opacity: 0.9;
}

.footer-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-section li {
  padding: 5px 0;
  opacity: 0.9;
  transition: opacity 0.3s ease;
}

.footer-section li:hover {
  opacity: 1;
  color: #3498db;
}

.version {
  font-size: 0.9rem;
  color: #95a5a6;
  font-style: italic;
}

.footer-bottom {
  border-top: 1px solid #34495e;
  margin-top: 30px;
  padding-top: 20px;
  text-align: center;
}

.footer-bottom p {
  margin: 0;
  opacity: 0.8;
  font-size: 0.9rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .app-footer {
    padding: 30px 0 15px 0;
  }
}
