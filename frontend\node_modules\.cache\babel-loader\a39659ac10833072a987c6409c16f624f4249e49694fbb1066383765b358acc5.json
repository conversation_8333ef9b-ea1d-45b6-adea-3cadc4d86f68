{"ast": null, "code": "var _jsxFileName = \"D:\\\\Desktop files\\\\My New Desktop\\\\Irrigation Engineering and Structure Design\\\\frontend\\\\src\\\\components\\\\GridConfig.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction GridConfig({\n  config,\n  onConfigChange\n}) {\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    onConfigChange({\n      ...config,\n      [name]: Number(value)\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"grid-config\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Grid Configuration\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"config-item\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        children: \"<PERSON><PERSON>a <PERSON> (in acres):\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"number\",\n        name: \"murabaSize\",\n        value: config.murabaSize,\n        onChange: handleInputChange,\n        min: \"1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"config-item\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        children: \"Killa Size (in acres):\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"number\",\n        name: \"killaSize\",\n        value: config.killaSize,\n        onChange: handleInputChange,\n        min: \"1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"config-item\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        children: \"East-West Unit (in feet):\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"number\",\n        name: \"eastWestUnit\",\n        value: config.eastWestUnit,\n        onChange: handleInputChange,\n        min: \"1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"config-item\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        children: \"North-South Unit (in feet):\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"number\",\n        name: \"northSouthUnit\",\n        value: config.northSouthUnit,\n        onChange: handleInputChange,\n        min: \"1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"note\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Note:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 12\n        }, this), \" A standard Muraba is 5x5 acres (25 acres total).\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Each Killa is typically 1 acre (220 ft East-West \\xD7 198 ft North-South).\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 5\n  }, this);\n}\n_c = GridConfig;\nexport default GridConfig;\nvar _c;\n$RefreshReg$(_c, \"GridConfig\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "GridConfig", "config", "onConfigChange", "handleInputChange", "e", "name", "value", "target", "Number", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "murabaSize", "onChange", "min", "killa<PERSON>ize", "eastWestUnit", "northSouthUnit", "_c", "$RefreshReg$"], "sources": ["D:/Desktop files/My New Desktop/Irrigation Engineering and Structure Design/frontend/src/components/GridConfig.js"], "sourcesContent": ["import React from 'react';\r\n\r\nfunction GridConfig({ config, onConfigChange }) {\r\n  const handleInputChange = (e) => {\r\n    const { name, value } = e.target;\r\n    onConfigChange({\r\n      ...config,\r\n      [name]: Number(value)\r\n    });\r\n  };\r\n\r\n  return (\r\n    <div className=\"grid-config\">\r\n      <h2>Grid Configuration</h2>\r\n      <div className=\"config-item\">\r\n        <label>Muraba Size (in acres):</label>\r\n        <input \r\n          type=\"number\" \r\n          name=\"murabaSize\"\r\n          value={config.murabaSize}\r\n          onChange={handleInputChange}\r\n          min=\"1\"\r\n        />\r\n      </div>\r\n      <div className=\"config-item\">\r\n        <label>Killa Size (in acres):</label>\r\n        <input \r\n          type=\"number\" \r\n          name=\"killaSize\"\r\n          value={config.killaSize}\r\n          onChange={handleInputChange}\r\n          min=\"1\"\r\n        />\r\n      </div>\r\n      <div className=\"config-item\">\r\n        <label>East-West Unit (in feet):</label>\r\n        <input \r\n          type=\"number\" \r\n          name=\"eastWestUnit\"\r\n          value={config.eastWestUnit}\r\n          onChange={handleInputChange}\r\n          min=\"1\"\r\n        />\r\n      </div>\r\n      <div className=\"config-item\">\r\n        <label>North-South Unit (in feet):</label>\r\n        <input \r\n          type=\"number\" \r\n          name=\"northSouthUnit\"\r\n          value={config.northSouthUnit}\r\n          onChange={handleInputChange}\r\n          min=\"1\"\r\n        />\r\n      </div>\r\n      <div className=\"note\">\r\n        <p><strong>Note:</strong> A standard Muraba is 5x5 acres (25 acres total).</p>\r\n        <p>Each Killa is typically 1 acre (220 ft East-West × 198 ft North-South).</p>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default GridConfig;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,SAASC,UAAUA,CAAC;EAAEC,MAAM;EAAEC;AAAe,CAAC,EAAE;EAC9C,MAAMC,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCL,cAAc,CAAC;MACb,GAAGD,MAAM;MACT,CAACI,IAAI,GAAGG,MAAM,CAACF,KAAK;IACtB,CAAC,CAAC;EACJ,CAAC;EAED,oBACEP,OAAA;IAAKU,SAAS,EAAC,aAAa;IAAAC,QAAA,gBAC1BX,OAAA;MAAAW,QAAA,EAAI;IAAkB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC3Bf,OAAA;MAAKU,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BX,OAAA;QAAAW,QAAA,EAAO;MAAuB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACtCf,OAAA;QACEgB,IAAI,EAAC,QAAQ;QACbV,IAAI,EAAC,YAAY;QACjBC,KAAK,EAAEL,MAAM,CAACe,UAAW;QACzBC,QAAQ,EAAEd,iBAAkB;QAC5Be,GAAG,EAAC;MAAG;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eACNf,OAAA;MAAKU,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BX,OAAA;QAAAW,QAAA,EAAO;MAAsB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACrCf,OAAA;QACEgB,IAAI,EAAC,QAAQ;QACbV,IAAI,EAAC,WAAW;QAChBC,KAAK,EAAEL,MAAM,CAACkB,SAAU;QACxBF,QAAQ,EAAEd,iBAAkB;QAC5Be,GAAG,EAAC;MAAG;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eACNf,OAAA;MAAKU,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BX,OAAA;QAAAW,QAAA,EAAO;MAAyB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACxCf,OAAA;QACEgB,IAAI,EAAC,QAAQ;QACbV,IAAI,EAAC,cAAc;QACnBC,KAAK,EAAEL,MAAM,CAACmB,YAAa;QAC3BH,QAAQ,EAAEd,iBAAkB;QAC5Be,GAAG,EAAC;MAAG;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eACNf,OAAA;MAAKU,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BX,OAAA;QAAAW,QAAA,EAAO;MAA2B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC1Cf,OAAA;QACEgB,IAAI,EAAC,QAAQ;QACbV,IAAI,EAAC,gBAAgB;QACrBC,KAAK,EAAEL,MAAM,CAACoB,cAAe;QAC7BJ,QAAQ,EAAEd,iBAAkB;QAC5Be,GAAG,EAAC;MAAG;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eACNf,OAAA;MAAKU,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBX,OAAA;QAAAW,QAAA,gBAAGX,OAAA;UAAAW,QAAA,EAAQ;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,qDAAiD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC9Ef,OAAA;QAAAW,QAAA,EAAG;MAAuE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3E,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACQ,EAAA,GA1DQtB,UAAU;AA4DnB,eAAeA,UAAU;AAAC,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}