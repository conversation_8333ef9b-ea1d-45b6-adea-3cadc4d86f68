# @turf/center-median

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## centerMedian

Takes a [FeatureCollection][1] of points and calculates the median center,
algorithimically. The median center is understood as the point that is
requires the least total travel from all other points.

Turfjs has four different functions for calculating the center of a set of
data. Each is useful depending on circumstance.

`@turf/center` finds the simple center of a dataset, by finding the
midpoint between the extents of the data. That is, it divides in half the
farthest east and farthest west point as well as the farthest north and
farthest south.

`@turf/center-of-mass` imagines that the dataset is a sheet of paper.
The center of mass is where the sheet would balance on a fingertip.

`@turf/center-mean` takes the averages of all the coordinates and
produces a value that respects that. Unlike `@turf/center`, it is
sensitive to clusters and outliers. It lands in the statistical middle of a
dataset, not the geographical. It can also be weighted, meaning certain
points are more important than others.

`@turf/center-median` takes the mean center and tries to find, iteratively,
a new point that requires the least amount of travel from all the points in
the dataset. It is not as sensitive to outliers as `@turf/center`, but it is
attracted to clustered data. It, too, can be weighted.

**Bibliography**

<PERSON> and <PERSON>, “An Efficient Algorithm for the
Numerical Solution of the Generalized Weber Problem in Spatial
Economics,” _Journal of Regional Science_ 4, no. 2 (1962): 21–33,
doi:[https://doi.org/10.1111/j.1467-9787.1962.tb00902.x][2].

James E. Burt, Gerald M. Barber, and David L. Rigby, _Elementary
Statistics for Geographers_, 3rd ed., New York: The Guilford
Press, 2009, 150–151.

**Parameters**

-   `features` **[FeatureCollection][3]&lt;any>** Any GeoJSON Feature Collection
-   `options` **[Object][4]** Optional parameters (optional, default `{}`)
    -   `options.weight` **[string][5]?** the property name used to weight the center
    -   `options.tolerance` **[number][6]** the difference in distance between candidate medians at which point the algorighim stops iterating. (optional, default `0.001`)
    -   `options.counter` **[number][6]** how many attempts to find the median, should the tolerance be insufficient. (optional, default `10`)

**Examples**

```javascript
var points = turf.points([[0, 0], [1, 0], [0, 1], [5, 8]]);
var medianCenter = turf.centerMedian(points);

//addToMap
var addToMap = [points, medianCenter]
```

Returns **[Feature][7]&lt;[Point][8]>** The median center of the collection

[1]: https://tools.ietf.org/html/rfc7946#section-3.3

[2]: https://doi.org/10.1111/j.1467-9787.1962.tb00902.x

[3]: https://tools.ietf.org/html/rfc7946#section-3.3

[4]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[5]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String

[6]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number

[7]: https://tools.ietf.org/html/rfc7946#section-3.2

[8]: https://tools.ietf.org/html/rfc7946#section-3.1.2

<!-- This file is automatically generated. Please don't edit it directly:
if you find an error, edit the source file (likely index.js), and re-run
./scripts/generate-readmes in the turf project. -->

---

This module is part of the [Turfjs project](http://turfjs.org/), an open source
module collection dedicated to geographic algorithms. It is maintained in the
[Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create
PRs and issues.

### Installation

Install this module individually:

```sh
$ npm install @turf/center-median
```

Or install the Turf module that includes it as a function:

```sh
$ npm install @turf/turf
```
