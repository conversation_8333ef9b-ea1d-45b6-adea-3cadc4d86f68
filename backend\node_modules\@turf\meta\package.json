{"name": "@turf/meta", "version": "6.5.0", "description": "turf meta module", "author": "Turf Authors", "contributors": ["<PERSON> <@tmcw>", "<PERSON> <@dpmcmlxxvi>", "<PERSON> <@DenisCarriere>"], "license": "MIT", "bugs": {"url": "https://github.com/Turfjs/turf/issues"}, "homepage": "https://github.com/Turfjs/turf", "repository": {"type": "git", "url": "git://github.com/Turfjs/turf.git"}, "funding": "https://opencollective.com/turf", "publishConfig": {"access": "public"}, "keywords": ["functional", "programming", "turfjs", "g<PERSON><PERSON><PERSON>", "meta", "flattenEach", "flattenReduce", "segmentEach", "segmentReduce", "coordEach", "coordReduce", "propEach", "propReduce", "featureEach", "featureReduce", "coordAll", "geomEach", "geomReduce", "lineEeach", "lineReduce"], "main": "dist/js/index.js", "module": "dist/es/index.js", "exports": {"./package.json": "./package.json", ".": {"import": "./dist/es/index.js", "require": "./dist/js/index.js"}}, "types": "index.d.ts", "sideEffects": false, "files": ["dist", "index.d.ts"], "scripts": {"bench": "node -r esm bench.js", "build": "rollup -c ../../rollup.config.js && echo '{\"type\":\"module\"}' > dist/es/package.json", "docs": "node ../../scripts/generate-readmes", "test": "npm-run-all test:*", "test:tape": "node -r esm test.js", "test:types": "tsc --esModuleInterop --noEmit types.ts"}, "devDependencies": {"@turf/random": "^6.5.0", "benchmark": "*", "npm-run-all": "*", "rollup": "*", "tape": "*"}, "dependencies": {"@turf/helpers": "^6.5.0"}, "gitHead": "5375941072b90d489389db22b43bfe809d5e451e"}