{"ast": null, "code": "var _jsxFileName = \"D:\\\\Desktop files\\\\My New Desktop\\\\Irrigation Engineering and Structure Design\\\\frontend\\\\src\\\\components\\\\Header.js\";\nimport React from 'react';\nimport './Header.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Header() {\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"app-header\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"header-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"logo-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"\\uD83C\\uDFD7\\uFE0F Irrigation Engineering Portal\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 9,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Complete Solution for Irrigation Department Engineers\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"navigation\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"nav-item active\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\uD83D\\uDCD0 Chak Plan Designer\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 14,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"nav-item\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\uD83C\\uDF0A Canal Design (Coming Soon)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"nav-item\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\u26A1 Outlet Design (Coming Soon)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 20,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"nav-item\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\uD83C\\uDFD7\\uFE0F Hydraulic Structures (Coming Soon)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"header-info\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"info-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"About Chak Plan\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"A Chak Plan (also called Sazra Plan) is a systematic layout of agricultural land divided into Murabas and Killas for efficient irrigation management. This tool helps engineers design and calculate optimal land distribution patterns.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"info-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Key Features\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Interactive boundary drawing\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Automatic area calculations\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Grid generation with custom parameters\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Canal network visualization\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Export functionality for documentation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"info-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Standard Units\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Muraba:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 17\n            }, this), \" 25 acres (5\\xD75 acres)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Killa:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 17\n            }, this), \" 1 acre (220\\xD7198 feet)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Standard Grid:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 17\n            }, this), \" 220 ft E-W \\xD7 198 ft N-S\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n}\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Header", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Desktop files/My New Desktop/Irrigation Engineering and Structure Design/frontend/src/components/Header.js"], "sourcesContent": ["import React from 'react';\nimport './Header.css';\n\nfunction Header() {\n  return (\n    <header className=\"app-header\">\n      <div className=\"header-content\">\n        <div className=\"logo-section\">\n          <h1>🏗️ Irrigation Engineering Portal</h1>\n          <p>Complete Solution for Irrigation Department Engineers</p>\n        </div>\n        <nav className=\"navigation\">\n          <div className=\"nav-item active\">\n            <span>📐 Chak Plan Designer</span>\n          </div>\n          <div className=\"nav-item\">\n            <span>🌊 Canal Design (Coming Soon)</span>\n          </div>\n          <div className=\"nav-item\">\n            <span>⚡ Outlet Design (Coming Soon)</span>\n          </div>\n          <div className=\"nav-item\">\n            <span>🏗️ Hydraulic Structures (Coming Soon)</span>\n          </div>\n        </nav>\n      </div>\n      <div className=\"header-info\">\n        <div className=\"info-card\">\n          <h3>About Chak Plan</h3>\n          <p>\n            A Chak Plan (also called Sazra Plan) is a systematic layout of agricultural land \n            divided into Murabas and Killas for efficient irrigation management. This tool \n            helps engineers design and calculate optimal land distribution patterns.\n          </p>\n        </div>\n        <div className=\"info-card\">\n          <h3>Key Features</h3>\n          <ul>\n            <li>Interactive boundary drawing</li>\n            <li>Automatic area calculations</li>\n            <li>Grid generation with custom parameters</li>\n            <li>Canal network visualization</li>\n            <li>Export functionality for documentation</li>\n          </ul>\n        </div>\n        <div className=\"info-card\">\n          <h3>Standard Units</h3>\n          <ul>\n            <li><strong>Muraba:</strong> 25 acres (5×5 acres)</li>\n            <li><strong>Killa:</strong> 1 acre (220×198 feet)</li>\n            <li><strong>Standard Grid:</strong> 220 ft E-W × 198 ft N-S</li>\n          </ul>\n        </div>\n      </div>\n    </header>\n  );\n}\n\nexport default Header;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,SAASC,MAAMA,CAAA,EAAG;EAChB,oBACED,OAAA;IAAQE,SAAS,EAAC,YAAY;IAAAC,QAAA,gBAC5BH,OAAA;MAAKE,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BH,OAAA;QAAKE,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BH,OAAA;UAAAG,QAAA,EAAI;QAAiC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1CP,OAAA;UAAAG,QAAA,EAAG;QAAqD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC,eACNP,OAAA;QAAKE,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBH,OAAA;UAAKE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BH,OAAA;YAAAG,QAAA,EAAM;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eACNP,OAAA;UAAKE,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBH,OAAA;YAAAG,QAAA,EAAM;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eACNP,OAAA;UAAKE,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBH,OAAA;YAAAG,QAAA,EAAM;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eACNP,OAAA;UAAKE,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBH,OAAA;YAAAG,QAAA,EAAM;UAAsC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNP,OAAA;MAAKE,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BH,OAAA;QAAKE,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBH,OAAA;UAAAG,QAAA,EAAI;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxBP,OAAA;UAAAG,QAAA,EAAG;QAIH;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACNP,OAAA;QAAKE,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBH,OAAA;UAAAG,QAAA,EAAI;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrBP,OAAA;UAAAG,QAAA,gBACEH,OAAA;YAAAG,QAAA,EAAI;UAA4B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrCP,OAAA;YAAAG,QAAA,EAAI;UAA2B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpCP,OAAA;YAAAG,QAAA,EAAI;UAAsC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/CP,OAAA;YAAAG,QAAA,EAAI;UAA2B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpCP,OAAA;YAAAG,QAAA,EAAI;UAAsC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACNP,OAAA;QAAKE,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBH,OAAA;UAAAG,QAAA,EAAI;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvBP,OAAA;UAAAG,QAAA,gBACEH,OAAA;YAAAG,QAAA,gBAAIH,OAAA;cAAAG,QAAA,EAAQ;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,4BAAqB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtDP,OAAA;YAAAG,QAAA,gBAAIH,OAAA;cAAAG,QAAA,EAAQ;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,6BAAsB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtDP,OAAA;YAAAG,QAAA,gBAAIH,OAAA;cAAAG,QAAA,EAAQ;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,+BAAwB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb;AAACC,EAAA,GArDQP,MAAM;AAuDf,eAAeA,MAAM;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}