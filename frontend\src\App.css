.app {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    min-height: 100vh;
    background-color: #f5f7fa;
}

.main-app-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

h1 {
    color: #2c3e50;
    text-align: center;
    margin-bottom: 30px;
}

.main-content {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.drawing-canvas {
    flex: 3;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
}

.grid-config {
    flex: 1;
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #eee;
}

.config-item {
    margin-bottom: 15px;
}

.config-item label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.config-item input {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.note {
    margin-top: 20px;
    padding: 10px;
    background-color: #e8f4fc;
    border-left: 4px solid #3498db;
    font-size: 0.9rem;
}

.calculation-result {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #eee;
    margin-top: 20px;
}

.calculation-result h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    border-bottom: 2px solid #3498db;
    padding-bottom: 8px;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background-color: white;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.summary-item label {
    font-weight: 600;
    color: #34495e;
}

.summary-item span {
    font-weight: 500;
    color: #2980b9;
    background-color: #ecf0f1;
    padding: 4px 8px;
    border-radius: 4px;
}

.export-options {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-top: 15px;
}

.export-btn,
.clear-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.export-btn {
    background-color: #27ae60;
    color: white;
}

.export-btn:hover {
    background-color: #229954;
    transform: translateY(-1px);
}

.clear-btn {
    background-color: #e74c3c;
    color: white;
}

.clear-btn:hover {
    background-color: #c0392b;
    transform: translateY(-1px);
}

/* Responsive design */
@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
    }

    .summary-grid {
        grid-template-columns: 1fr;
    }

    .export-options {
        flex-direction: column;
    }
}

/* Leaflet map enhancements */
.leaflet-control-layers {
    background-color: rgba(255, 255, 255, 0.95);
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.leaflet-control-draw {
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Custom tooltip styles */
.leaflet-tooltip {
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    border-radius: 4px;
    padding: 5px 8px;
    font-size: 12px;
}