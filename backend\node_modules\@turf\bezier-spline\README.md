# @turf/bezier-spline

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## bezierSpline

Takes a [line][1] and returns a curved version
by applying a [Bezier spline][2]
algorithm.

The bezier spline implementation is by [<PERSON><PERSON><PERSON>][3].

**Parameters**

-   `line` **[Feature][4]&lt;[LineString][5]>** input LineString
-   `options` **[Object][6]** Optional parameters (optional, default `{}`)
    -   `options.properties` **[Object][6]** Translate properties to output (optional, default `{}`)
    -   `options.resolution` **[number][7]** time in milliseconds between points (optional, default `10000`)
    -   `options.sharpness` **[number][7]** a measure of how curvy the path should be between splines (optional, default `0.85`)

**Examples**

```javascript
var line = turf.lineString([
  [-76.091308, 18.427501],
  [-76.695556, 18.729501],
  [-76.552734, 19.40443],
  [-74.61914, 19.134789],
  [-73.652343, 20.07657],
  [-73.157958, 20.210656]
]);

var curved = turf.bezierSpline(line);

//addToMap
var addToMap = [line, curved]
curved.properties = { stroke: '#0F0' };
```

Returns **[Feature][4]&lt;[LineString][5]>** curved line

[1]: https://tools.ietf.org/html/rfc7946#section-3.1.4

[2]: http://en.wikipedia.org/wiki/B%C3%A9zier_spline

[3]: http://leszek.rybicki.cc/

[4]: https://tools.ietf.org/html/rfc7946#section-3.2

[5]: https://tools.ietf.org/html/rfc7946#section-3.1.4

[6]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[7]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number

<!-- This file is automatically generated. Please don't edit it directly:
if you find an error, edit the source file (likely index.js), and re-run
./scripts/generate-readmes in the turf project. -->

---

This module is part of the [Turfjs project](http://turfjs.org/), an open source
module collection dedicated to geographic algorithms. It is maintained in the
[Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create
PRs and issues.

### Installation

Install this module individually:

```sh
$ npm install @turf/bezier-spline
```

Or install the Turf module that includes it as a function:

```sh
$ npm install @turf/turf
```
