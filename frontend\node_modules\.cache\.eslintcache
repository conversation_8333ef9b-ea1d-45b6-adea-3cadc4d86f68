[{"D:\\Desktop files\\My New Desktop\\Irrigation Engineering and Structure Design\\frontend\\src\\index.js": "1", "D:\\Desktop files\\My New Desktop\\Irrigation Engineering and Structure Design\\frontend\\src\\App.js": "2", "D:\\Desktop files\\My New Desktop\\Irrigation Engineering and Structure Design\\frontend\\src\\components\\GridConfig.js": "3", "D:\\Desktop files\\My New Desktop\\Irrigation Engineering and Structure Design\\frontend\\src\\components\\DrawingCanvas.js": "4"}, {"size": 263, "mtime": 1748864094294, "results": "5", "hashOfConfig": "6"}, {"size": 1205, "mtime": 1748863880374, "results": "7", "hashOfConfig": "6"}, {"size": 1727, "mtime": 1748863985247, "results": "8", "hashOfConfig": "6"}, {"size": 3040, "mtime": 1748866264983, "results": "9", "hashOfConfig": "6"}, {"filePath": "10", "messages": "11", "suppressedMessages": "12", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "o368yf", {"filePath": "13", "messages": "14", "suppressedMessages": "15", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Desktop files\\My New Desktop\\Irrigation Engineering and Structure Design\\frontend\\src\\index.js", [], [], "D:\\Desktop files\\My New Desktop\\Irrigation Engineering and Structure Design\\frontend\\src\\App.js", ["22"], [], "D:\\Desktop files\\My New Desktop\\Irrigation Engineering and Structure Design\\frontend\\src\\components\\GridConfig.js", [], [], "D:\\Desktop files\\My New Desktop\\Irrigation Engineering and Structure Design\\frontend\\src\\components\\DrawingCanvas.js", ["23", "24", "25"], [], {"ruleId": "26", "severity": 1, "message": "27", "line": 1, "column": 27, "nodeType": "28", "messageId": "29", "endLine": 1, "endColumn": 33}, {"ruleId": "26", "severity": 1, "message": "30", "line": 11, "column": 10, "nodeType": "28", "messageId": "29", "endLine": 11, "endColumn": 21}, {"ruleId": "26", "severity": 1, "message": "31", "line": 14, "column": 21, "nodeType": "28", "messageId": "29", "endLine": 14, "endColumn": 33}, {"ruleId": "32", "severity": 1, "message": "33", "line": 75, "column": 6, "nodeType": "34", "endLine": 75, "endColumn": 38, "suggestions": "35"}, "no-unused-vars", "'useRef' is defined but never used.", "Identifier", "unusedVar", "'drawControl' is assigned a value but never used.", "'setGridLayer' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'gridLayer'. Either include it or remove the dependency array.", "ArrayExpression", ["36"], {"desc": "37", "fix": "38"}, "Update the dependencies array to be: [map, polygonPoints, gridConfig, gridLayer]", {"range": "39", "text": "40"}, [2181, 2213], "[map, polygonPoints, gridConfig, gridLayer]"]