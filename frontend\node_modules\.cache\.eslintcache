[{"D:\\Desktop files\\My New Desktop\\Irrigation Engineering and Structure Design\\frontend\\src\\index.js": "1", "D:\\Desktop files\\My New Desktop\\Irrigation Engineering and Structure Design\\frontend\\src\\App.js": "2", "D:\\Desktop files\\My New Desktop\\Irrigation Engineering and Structure Design\\frontend\\src\\components\\GridConfig.js": "3", "D:\\Desktop files\\My New Desktop\\Irrigation Engineering and Structure Design\\frontend\\src\\components\\DrawingCanvas.js": "4", "D:\\Desktop files\\My New Desktop\\Irrigation Engineering and Structure Design\\frontend\\src\\components\\Header.js": "5", "D:\\Desktop files\\My New Desktop\\Irrigation Engineering and Structure Design\\frontend\\src\\components\\Footer.js": "6"}, {"size": 263, "mtime": 1748864094294, "results": "7", "hashOfConfig": "8"}, {"size": 5427, "mtime": 1748866726933, "results": "9", "hashOfConfig": "8"}, {"size": 1727, "mtime": 1748863985247, "results": "10", "hashOfConfig": "8"}, {"size": 8348, "mtime": 1748866513706, "results": "11", "hashOfConfig": "8"}, {"size": 2050, "mtime": 1748866608437, "results": "12", "hashOfConfig": "8"}, {"size": 1514, "mtime": 1748866701890, "results": "13", "hashOfConfig": "8"}, {"filePath": "14", "messages": "15", "suppressedMessages": "16", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "o368yf", {"filePath": "17", "messages": "18", "suppressedMessages": "19", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Desktop files\\My New Desktop\\Irrigation Engineering and Structure Design\\frontend\\src\\index.js", [], [], "D:\\Desktop files\\My New Desktop\\Irrigation Engineering and Structure Design\\frontend\\src\\App.js", ["32"], [], "D:\\Desktop files\\My New Desktop\\Irrigation Engineering and Structure Design\\frontend\\src\\components\\GridConfig.js", [], [], "D:\\Desktop files\\My New Desktop\\Irrigation Engineering and Structure Design\\frontend\\src\\components\\DrawingCanvas.js", ["33"], [], "D:\\Desktop files\\My New Desktop\\Irrigation Engineering and Structure Design\\frontend\\src\\components\\Header.js", [], [], "D:\\Desktop files\\My New Desktop\\Irrigation Engineering and Structure Design\\frontend\\src\\components\\Footer.js", [], [], {"ruleId": "34", "severity": 1, "message": "35", "line": 1, "column": 27, "nodeType": "36", "messageId": "37", "endLine": 1, "endColumn": 33}, {"ruleId": "38", "severity": 2, "message": "39", "line": 226, "column": 14, "nodeType": "36", "messageId": "40", "endLine": 226, "endColumn": 20}, "no-unused-vars", "'useRef' is defined but never used.", "Identifier", "unusedVar", "no-undef", "'mapRef' is not defined.", "undef"]