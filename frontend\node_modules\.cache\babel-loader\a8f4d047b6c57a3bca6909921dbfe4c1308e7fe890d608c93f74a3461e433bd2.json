{"ast": null, "code": "function _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport { LeafletProvider, createLeafletContext } from '@react-leaflet/core';\nimport { Map as LeafletMap } from 'leaflet';\nimport React, { forwardRef, useCallback, useEffect, useImperativeHandle, useState } from 'react';\nfunction MapContainerComponent({\n  bounds,\n  boundsOptions,\n  center,\n  children,\n  className,\n  id,\n  placeholder,\n  style,\n  whenReady,\n  zoom,\n  ...options\n}, forwardedRef) {\n  const [props] = useState({\n    className,\n    id,\n    style\n  });\n  const [context, setContext] = useState(null);\n  useImperativeHandle(forwardedRef, () => context?.map ?? null, [context]);\n  const mapRef = useCallback(node => {\n    if (node !== null && context === null) {\n      const map = new LeafletMap(node, options);\n      if (center != null && zoom != null) {\n        map.setView(center, zoom);\n      } else if (bounds != null) {\n        map.fitBounds(bounds, boundsOptions);\n      }\n      if (whenReady != null) {\n        map.whenReady(whenReady);\n      }\n      setContext(createLeafletContext(map));\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  useEffect(() => {\n    return () => {\n      context?.map.remove();\n    };\n  }, [context]);\n  const contents = context ? /*#__PURE__*/React.createElement(LeafletProvider, {\n    value: context\n  }, children) : placeholder ?? null;\n  return /*#__PURE__*/React.createElement(\"div\", _extends({}, props, {\n    ref: mapRef\n  }), contents);\n}\nexport const MapContainer = /*#__PURE__*/forwardRef(MapContainerComponent);", "map": {"version": 3, "names": ["_extends", "Object", "assign", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "apply", "LeafletProvider", "createLeafletContext", "Map", "LeafletMap", "React", "forwardRef", "useCallback", "useEffect", "useImperativeHandle", "useState", "MapContainerComponent", "bounds", "boundsOptions", "center", "children", "className", "id", "placeholder", "style", "when<PERSON><PERSON><PERSON>", "zoom", "options", "forwardedRef", "props", "context", "setContext", "map", "mapRef", "node", "<PERSON><PERSON><PERSON><PERSON>", "fitBounds", "remove", "contents", "createElement", "value", "ref", "MapContainer"], "sources": ["D:/Desktop files/My New Desktop/Irrigation Engineering and Structure Design/frontend/node_modules/react-leaflet/lib/MapContainer.js"], "sourcesContent": ["function _extends() {\n    _extends = Object.assign || function(target) {\n        for(var i = 1; i < arguments.length; i++){\n            var source = arguments[i];\n            for(var key in source){\n                if (Object.prototype.hasOwnProperty.call(source, key)) {\n                    target[key] = source[key];\n                }\n            }\n        }\n        return target;\n    };\n    return _extends.apply(this, arguments);\n}\nimport { LeafletProvider, createLeafletContext } from '@react-leaflet/core';\nimport { Map as LeafletMap } from 'leaflet';\nimport React, { forwardRef, useCallback, useEffect, useImperativeHandle, useState } from 'react';\nfunction MapContainerComponent({ bounds , boundsOptions , center , children , className , id , placeholder , style , whenReady , zoom , ...options }, forwardedRef) {\n    const [props] = useState({\n        className,\n        id,\n        style\n    });\n    const [context, setContext] = useState(null);\n    useImperativeHandle(forwardedRef, ()=>context?.map ?? null, [\n        context\n    ]);\n    const mapRef = useCallback((node)=>{\n        if (node !== null && context === null) {\n            const map = new LeafletMap(node, options);\n            if (center != null && zoom != null) {\n                map.setView(center, zoom);\n            } else if (bounds != null) {\n                map.fitBounds(bounds, boundsOptions);\n            }\n            if (whenReady != null) {\n                map.whenReady(whenReady);\n            }\n            setContext(createLeafletContext(map));\n        }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, []);\n    useEffect(()=>{\n        return ()=>{\n            context?.map.remove();\n        };\n    }, [\n        context\n    ]);\n    const contents = context ? /*#__PURE__*/ React.createElement(LeafletProvider, {\n        value: context\n    }, children) : placeholder ?? null;\n    return /*#__PURE__*/ React.createElement(\"div\", _extends({}, props, {\n        ref: mapRef\n    }), contents);\n}\nexport const MapContainer = /*#__PURE__*/ forwardRef(MapContainerComponent);\n"], "mappings": "AAAA,SAASA,QAAQA,CAAA,EAAG;EAChBA,QAAQ,GAAGC,MAAM,CAACC,MAAM,IAAI,UAASC,MAAM,EAAE;IACzC,KAAI,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAC;MACrC,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC;MACzB,KAAI,IAAII,GAAG,IAAID,MAAM,EAAC;QAClB,IAAIN,MAAM,CAACQ,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,MAAM,EAAEC,GAAG,CAAC,EAAE;UACnDL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;QAC7B;MACJ;IACJ;IACA,OAAOL,MAAM;EACjB,CAAC;EACD,OAAOH,QAAQ,CAACY,KAAK,CAAC,IAAI,EAAEP,SAAS,CAAC;AAC1C;AACA,SAASQ,eAAe,EAAEC,oBAAoB,QAAQ,qBAAqB;AAC3E,SAASC,GAAG,IAAIC,UAAU,QAAQ,SAAS;AAC3C,OAAOC,KAAK,IAAIC,UAAU,EAAEC,WAAW,EAAEC,SAAS,EAAEC,mBAAmB,EAAEC,QAAQ,QAAQ,OAAO;AAChG,SAASC,qBAAqBA,CAAC;EAAEC,MAAM;EAAGC,aAAa;EAAGC,MAAM;EAAGC,QAAQ;EAAGC,SAAS;EAAGC,EAAE;EAAGC,WAAW;EAAGC,KAAK;EAAGC,SAAS;EAAGC,IAAI;EAAG,GAAGC;AAAQ,CAAC,EAAEC,YAAY,EAAE;EAChK,MAAM,CAACC,KAAK,CAAC,GAAGd,QAAQ,CAAC;IACrBM,SAAS;IACTC,EAAE;IACFE;EACJ,CAAC,CAAC;EACF,MAAM,CAACM,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC5CD,mBAAmB,CAACc,YAAY,EAAE,MAAIE,OAAO,EAAEE,GAAG,IAAI,IAAI,EAAE,CACxDF,OAAO,CACV,CAAC;EACF,MAAMG,MAAM,GAAGrB,WAAW,CAAEsB,IAAI,IAAG;IAC/B,IAAIA,IAAI,KAAK,IAAI,IAAIJ,OAAO,KAAK,IAAI,EAAE;MACnC,MAAME,GAAG,GAAG,IAAIvB,UAAU,CAACyB,IAAI,EAAEP,OAAO,CAAC;MACzC,IAAIR,MAAM,IAAI,IAAI,IAAIO,IAAI,IAAI,IAAI,EAAE;QAChCM,GAAG,CAACG,OAAO,CAAChB,MAAM,EAAEO,IAAI,CAAC;MAC7B,CAAC,MAAM,IAAIT,MAAM,IAAI,IAAI,EAAE;QACvBe,GAAG,CAACI,SAAS,CAACnB,MAAM,EAAEC,aAAa,CAAC;MACxC;MACA,IAAIO,SAAS,IAAI,IAAI,EAAE;QACnBO,GAAG,CAACP,SAAS,CAACA,SAAS,CAAC;MAC5B;MACAM,UAAU,CAACxB,oBAAoB,CAACyB,GAAG,CAAC,CAAC;IACzC;IACJ;EACA,CAAC,EAAE,EAAE,CAAC;EACNnB,SAAS,CAAC,MAAI;IACV,OAAO,MAAI;MACPiB,OAAO,EAAEE,GAAG,CAACK,MAAM,CAAC,CAAC;IACzB,CAAC;EACL,CAAC,EAAE,CACCP,OAAO,CACV,CAAC;EACF,MAAMQ,QAAQ,GAAGR,OAAO,GAAG,aAAcpB,KAAK,CAAC6B,aAAa,CAACjC,eAAe,EAAE;IAC1EkC,KAAK,EAAEV;EACX,CAAC,EAAEV,QAAQ,CAAC,GAAGG,WAAW,IAAI,IAAI;EAClC,OAAO,aAAcb,KAAK,CAAC6B,aAAa,CAAC,KAAK,EAAE9C,QAAQ,CAAC,CAAC,CAAC,EAAEoC,KAAK,EAAE;IAChEY,GAAG,EAAER;EACT,CAAC,CAAC,EAAEK,QAAQ,CAAC;AACjB;AACA,OAAO,MAAMI,YAAY,GAAG,aAAc/B,UAAU,CAACK,qBAAqB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}