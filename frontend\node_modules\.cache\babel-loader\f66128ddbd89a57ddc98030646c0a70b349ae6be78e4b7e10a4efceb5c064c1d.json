{"ast": null, "code": "import { LeafletProvider, addClassName, useLeafletContext } from '@react-leaflet/core';\nimport React, { forwardRef, useState, useEffect, useImperativeHandle, useMemo } from 'react';\nimport { createPortal } from 'react-dom';\nconst DEFAULT_PANES = ['mapPane', 'markerPane', 'overlayPane', 'popupPane', 'shadowPane', 'tilePane', 'tooltipPane'];\nfunction omitPane(obj, pane) {\n  const {\n    [pane]: _p,\n    ...others\n  } = obj;\n  return others;\n}\nfunction createPane(name, props, context) {\n  if (DEFAULT_PANES.indexOf(name) !== -1) {\n    throw new Error(`You must use a unique name for a pane that is not a default Leaflet pane: ${name}`);\n  }\n  if (context.map.getPane(name) != null) {\n    throw new Error(`A pane with this name already exists: ${name}`);\n  }\n  const parentPaneName = props.pane ?? context.pane;\n  const parentPane = parentPaneName ? context.map.getPane(parentPaneName) : undefined;\n  const element = context.map.createPane(name, parentPane);\n  if (props.className != null) {\n    addClassName(element, props.className);\n  }\n  if (props.style != null) {\n    Object.keys(props.style).forEach(key => {\n      // @ts-ignore\n      element.style[key] = props.style[key];\n    });\n  }\n  return element;\n}\nfunction PaneComponent(props, forwardedRef) {\n  const [paneName] = useState(props.name);\n  const [paneElement, setPaneElement] = useState(null);\n  useImperativeHandle(forwardedRef, () => paneElement, [paneElement]);\n  const context = useLeafletContext();\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  const newContext = useMemo(() => ({\n    ...context,\n    pane: paneName\n  }), [context]);\n  useEffect(() => {\n    setPaneElement(createPane(paneName, props, context));\n    return function removeCreatedPane() {\n      const pane = context.map.getPane(paneName);\n      pane?.remove?.();\n      // @ts-ignore map internals\n      if (context.map._panes != null) {\n        // @ts-ignore map internals\n        context.map._panes = omitPane(context.map._panes, paneName);\n        // @ts-ignore map internals\n        context.map._paneRenderers = omitPane(\n        // @ts-ignore map internals\n        context.map._paneRenderers, paneName);\n      }\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  return props.children != null && paneElement != null ? /*#__PURE__*/createPortal(/*#__PURE__*/React.createElement(LeafletProvider, {\n    value: newContext\n  }, props.children), paneElement) : null;\n}\nexport const Pane = /*#__PURE__*/forwardRef(PaneComponent);", "map": {"version": 3, "names": ["LeafletProvider", "addClassName", "useLeafletContext", "React", "forwardRef", "useState", "useEffect", "useImperativeHandle", "useMemo", "createPortal", "DEFAULT_PANES", "omitPane", "obj", "pane", "_p", "others", "createPane", "name", "props", "context", "indexOf", "Error", "map", "getPane", "parentPaneName", "parentPane", "undefined", "element", "className", "style", "Object", "keys", "for<PERSON>ach", "key", "PaneComponent", "forwardedRef", "paneName", "paneElement", "setPaneElement", "newContext", "removeCreatedPane", "remove", "_panes", "_paneRenderers", "children", "createElement", "value", "Pane"], "sources": ["D:/Desktop files/My New Desktop/Irrigation Engineering and Structure Design/frontend/node_modules/react-leaflet/lib/Pane.js"], "sourcesContent": ["import { LeafletProvider, addClassName, useLeafletContext } from '@react-leaflet/core';\nimport React, { forwardRef, useState, useEffect, useImperativeHandle, useMemo } from 'react';\nimport { createPortal } from 'react-dom';\nconst DEFAULT_PANES = [\n    'mapPane',\n    'markerPane',\n    'overlayPane',\n    'popupPane',\n    'shadowPane',\n    'tilePane',\n    'tooltipPane'\n];\nfunction omitPane(obj, pane) {\n    const { [pane]: _p , ...others } = obj;\n    return others;\n}\nfunction createPane(name, props, context) {\n    if (DEFAULT_PANES.indexOf(name) !== -1) {\n        throw new Error(`You must use a unique name for a pane that is not a default Leaflet pane: ${name}`);\n    }\n    if (context.map.getPane(name) != null) {\n        throw new Error(`A pane with this name already exists: ${name}`);\n    }\n    const parentPaneName = props.pane ?? context.pane;\n    const parentPane = parentPaneName ? context.map.getPane(parentPaneName) : undefined;\n    const element = context.map.createPane(name, parentPane);\n    if (props.className != null) {\n        addClassName(element, props.className);\n    }\n    if (props.style != null) {\n        Object.keys(props.style).forEach((key)=>{\n            // @ts-ignore\n            element.style[key] = props.style[key];\n        });\n    }\n    return element;\n}\nfunction PaneComponent(props, forwardedRef) {\n    const [paneName] = useState(props.name);\n    const [paneElement, setPaneElement] = useState(null);\n    useImperativeHandle(forwardedRef, ()=>paneElement, [\n        paneElement\n    ]);\n    const context = useLeafletContext();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    const newContext = useMemo(()=>({\n            ...context,\n            pane: paneName\n        }), [\n        context\n    ]);\n    useEffect(()=>{\n        setPaneElement(createPane(paneName, props, context));\n        return function removeCreatedPane() {\n            const pane = context.map.getPane(paneName);\n            pane?.remove?.();\n            // @ts-ignore map internals\n            if (context.map._panes != null) {\n                // @ts-ignore map internals\n                context.map._panes = omitPane(context.map._panes, paneName);\n                // @ts-ignore map internals\n                context.map._paneRenderers = omitPane(// @ts-ignore map internals\n                context.map._paneRenderers, paneName);\n            }\n        };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, []);\n    return props.children != null && paneElement != null ? /*#__PURE__*/ createPortal(/*#__PURE__*/ React.createElement(LeafletProvider, {\n        value: newContext\n    }, props.children), paneElement) : null;\n}\nexport const Pane = /*#__PURE__*/ forwardRef(PaneComponent);\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,YAAY,EAAEC,iBAAiB,QAAQ,qBAAqB;AACtF,OAAOC,KAAK,IAAIC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,mBAAmB,EAAEC,OAAO,QAAQ,OAAO;AAC5F,SAASC,YAAY,QAAQ,WAAW;AACxC,MAAMC,aAAa,GAAG,CAClB,SAAS,EACT,YAAY,EACZ,aAAa,EACb,WAAW,EACX,YAAY,EACZ,UAAU,EACV,aAAa,CAChB;AACD,SAASC,QAAQA,CAACC,GAAG,EAAEC,IAAI,EAAE;EACzB,MAAM;IAAE,CAACA,IAAI,GAAGC,EAAE;IAAG,GAAGC;EAAO,CAAC,GAAGH,GAAG;EACtC,OAAOG,MAAM;AACjB;AACA,SAASC,UAAUA,CAACC,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAE;EACtC,IAAIT,aAAa,CAACU,OAAO,CAACH,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;IACpC,MAAM,IAAII,KAAK,CAAC,6EAA6EJ,IAAI,EAAE,CAAC;EACxG;EACA,IAAIE,OAAO,CAACG,GAAG,CAACC,OAAO,CAACN,IAAI,CAAC,IAAI,IAAI,EAAE;IACnC,MAAM,IAAII,KAAK,CAAC,yCAAyCJ,IAAI,EAAE,CAAC;EACpE;EACA,MAAMO,cAAc,GAAGN,KAAK,CAACL,IAAI,IAAIM,OAAO,CAACN,IAAI;EACjD,MAAMY,UAAU,GAAGD,cAAc,GAAGL,OAAO,CAACG,GAAG,CAACC,OAAO,CAACC,cAAc,CAAC,GAAGE,SAAS;EACnF,MAAMC,OAAO,GAAGR,OAAO,CAACG,GAAG,CAACN,UAAU,CAACC,IAAI,EAAEQ,UAAU,CAAC;EACxD,IAAIP,KAAK,CAACU,SAAS,IAAI,IAAI,EAAE;IACzB3B,YAAY,CAAC0B,OAAO,EAAET,KAAK,CAACU,SAAS,CAAC;EAC1C;EACA,IAAIV,KAAK,CAACW,KAAK,IAAI,IAAI,EAAE;IACrBC,MAAM,CAACC,IAAI,CAACb,KAAK,CAACW,KAAK,CAAC,CAACG,OAAO,CAAEC,GAAG,IAAG;MACpC;MACAN,OAAO,CAACE,KAAK,CAACI,GAAG,CAAC,GAAGf,KAAK,CAACW,KAAK,CAACI,GAAG,CAAC;IACzC,CAAC,CAAC;EACN;EACA,OAAON,OAAO;AAClB;AACA,SAASO,aAAaA,CAAChB,KAAK,EAAEiB,YAAY,EAAE;EACxC,MAAM,CAACC,QAAQ,CAAC,GAAG/B,QAAQ,CAACa,KAAK,CAACD,IAAI,CAAC;EACvC,MAAM,CAACoB,WAAW,EAAEC,cAAc,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EACpDE,mBAAmB,CAAC4B,YAAY,EAAE,MAAIE,WAAW,EAAE,CAC/CA,WAAW,CACd,CAAC;EACF,MAAMlB,OAAO,GAAGjB,iBAAiB,CAAC,CAAC;EACnC;EACA,MAAMqC,UAAU,GAAG/B,OAAO,CAAC,OAAK;IACxB,GAAGW,OAAO;IACVN,IAAI,EAAEuB;EACV,CAAC,CAAC,EAAE,CACJjB,OAAO,CACV,CAAC;EACFb,SAAS,CAAC,MAAI;IACVgC,cAAc,CAACtB,UAAU,CAACoB,QAAQ,EAAElB,KAAK,EAAEC,OAAO,CAAC,CAAC;IACpD,OAAO,SAASqB,iBAAiBA,CAAA,EAAG;MAChC,MAAM3B,IAAI,GAAGM,OAAO,CAACG,GAAG,CAACC,OAAO,CAACa,QAAQ,CAAC;MAC1CvB,IAAI,EAAE4B,MAAM,GAAG,CAAC;MAChB;MACA,IAAItB,OAAO,CAACG,GAAG,CAACoB,MAAM,IAAI,IAAI,EAAE;QAC5B;QACAvB,OAAO,CAACG,GAAG,CAACoB,MAAM,GAAG/B,QAAQ,CAACQ,OAAO,CAACG,GAAG,CAACoB,MAAM,EAAEN,QAAQ,CAAC;QAC3D;QACAjB,OAAO,CAACG,GAAG,CAACqB,cAAc,GAAGhC,QAAQ;QAAC;QACtCQ,OAAO,CAACG,GAAG,CAACqB,cAAc,EAAEP,QAAQ,CAAC;MACzC;IACJ,CAAC;IACL;EACA,CAAC,EAAE,EAAE,CAAC;EACN,OAAOlB,KAAK,CAAC0B,QAAQ,IAAI,IAAI,IAAIP,WAAW,IAAI,IAAI,GAAG,aAAc5B,YAAY,CAAC,aAAcN,KAAK,CAAC0C,aAAa,CAAC7C,eAAe,EAAE;IACjI8C,KAAK,EAAEP;EACX,CAAC,EAAErB,KAAK,CAAC0B,QAAQ,CAAC,EAAEP,WAAW,CAAC,GAAG,IAAI;AAC3C;AACA,OAAO,MAAMU,IAAI,GAAG,aAAc3C,UAAU,CAAC8B,aAAa,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}