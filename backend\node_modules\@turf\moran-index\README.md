# @turf/moran-index

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## moranIndex

<PERSON>'s I measures patterns of attribute values associated with features.
The method reveal whether similar values tend to occur near each other,
or whether high or low values are interspersed.

<PERSON>'s I > 0 means a clusterd pattern.
<PERSON>'s I &lt; 0 means a dispersed pattern.
<PERSON>'s I = 0 means a random pattern.

In order to test the significance of the result. The z score is calculated.
A positive enough z-score (ex. >1.96) indicates clustering,
while a negative enough z-score (ex. &lt;-1.96) indicates a dispersed pattern.

the z-score can be calculated based on a normal or random assumption.

**Bibliography\***

1.  [<PERSON>'s I](https://en.wikipedia.org/wiki/Moran%27s_I)

2.  [pysal](http://pysal.readthedocs.io/en/latest/index.html)

3.  <PERSON>, The ESRI Guide to GIS Analysis Volume 2: Spatial Measurements & Statistics.

**Parameters**

-   `fc` **[FeatureCollection](https://tools.ietf.org/html/rfc7946#section-3.3)&lt;any>** 
-   `options` **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)** 
    -   `options.inputField` **[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** the property name, must contain numeric values
    -   `options.threshold` **[number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)** the distance threshold (optional, default `100000`)
    -   `options.p` **[number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)** the Minkowski p-norm distance parameter (optional, default `2`)
    -   `options.binary` **[boolean](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** whether transfrom the distance to binary (optional, default `false`)
    -   `options.alpha` **[number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)** the distance decay parameter (optional, default `-1`)
    -   `options.standardization` **[boolean](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** wheter row standardization the distance (optional, default `true`)

**Examples**

```javascript
const bbox = [-65, 40, -63, 42];
const dataset = turf.randomPoint(100, { bbox: bbox });

const result = moranIndex(pts, {
  inputField: 'CRIME',
});
```

Returns **[MoranIndex](#moranindex)** 

## mean

get mean of a list

**Parameters**

-   `y` **[Array](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array)&lt;[number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)>** 

Returns **[number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)** 

## variance

get variance of a list

**Parameters**

-   `y` **[Array](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array)&lt;[number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)>** 

Returns **[number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)** 

## MoranIndex

Type: [Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)

**Properties**

-   `moranIndex` **[number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)** the moran's Index of the observed feature set
-   `expectedMoranIndex` **[number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)** the moran's Index of the random distribution
-   `stdNorm` **[number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)** the standard devitaion of the random distribution
-   `zNorm` **[number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)** the z-score of the observe samples with regard to the random distribution

<!-- This file is automatically generated. Please don't edit it directly:
if you find an error, edit the source file (likely index.js), and re-run
./scripts/generate-readmes in the turf project. -->

---

This module is part of the [Turfjs project](http://turfjs.org/), an open source
module collection dedicated to geographic algorithms. It is maintained in the
[Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create
PRs and issues.

### Installation

Install this module individually:

```sh
$ npm install @turf/moran-index
```

Or install the Turf module that includes it as a function:

```sh
$ npm install @turf/turf
```
