import React, { useState, useRef } from 'react';
import Header from './components/Header';
import DrawingCanvas from './components/DrawingCanvas';
import GridConfig from './components/GridConfig';
import Footer from './components/Footer';
import './App.css';

function App() {
    const [chakBoundary, setChakBoundary] = useState([]);
    const [chakArea, setChakArea] = useState(0);
    const [gridConfig, setGridConfig] = useState({
        murabaSize: 5,
        killaSize: 1,
        eastWestUnit: 220,
        northSouthUnit: 198
    });

    // Calculate area using Shoelace formula
    const calculatePolygonArea = (points) => {
        if (points.length < 3) return 0;

        let area = 0;
        for (let i = 0; i < points.length; i++) {
            const j = (i + 1) % points.length;
            area += points[i][0] * points[j][1];
            area -= points[j][0] * points[i][1];
        }
        area = Math.abs(area) / 2;

        // Convert from square degrees to square meters (approximate)
        const metersPerDegree = 111320; // at equator
        const areaInSquareMeters = area * Math.pow(metersPerDegree, 2);

        // Convert to acres (1 acre = 4047 square meters)
        return areaInSquareMeters / 4047;
    };

    const handleBoundaryComplete = (points) => {
        setChakBoundary(points);
        const area = calculatePolygonArea(points);
        setChakArea(area);
    };

    const exportChakData = () => {
        const chakData = {
            boundary: chakBoundary,
            area: chakArea,
            gridConfig: gridConfig,
            timestamp: new Date().toISOString(),
            calculations: {
                areaInAcres: chakArea,
                areaInHectares: chakArea * 0.4047,
                areaInSquareFeet: chakArea * 43560,
                estimatedMurabas: Math.floor(chakArea / (gridConfig.murabaSize * gridConfig.murabaSize)),
                estimatedKillas: Math.floor(chakArea / gridConfig.killaSize)
            }
        };

        const dataStr = JSON.stringify(chakData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `chak-plan-${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        URL.revokeObjectURL(url);
    };

    const clearChakData = () => {
        setChakBoundary([]);
        setChakArea(0);
    };

    return (
        <div className="app">
            <Header />
            <div className="main-app-content">
                <div className="main-content">
                    <DrawingCanvas
                        onBoundaryComplete={handleBoundaryComplete}
                        gridConfig={gridConfig}
                    />
                    <GridConfig
                        config={gridConfig}
                        onConfigChange={setGridConfig}
                    />
                </div>
                {chakBoundary.length > 0 && (
                    <div className="calculation-result">
                        <h3>Chak Plan Summary</h3>
                        <div className="summary-grid">
                            <div className="summary-item">
                                <label>Boundary Points:</label>
                                <span>{chakBoundary.length}</span>
                            </div>
                            <div className="summary-item">
                                <label>Total Area:</label>
                                <span>{chakArea.toFixed(2)} acres</span>
                            </div>
                            <div className="summary-item">
                                <label>Estimated Murabas:</label>
                                <span>{Math.floor(chakArea / (gridConfig.murabaSize * gridConfig.murabaSize))}</span>
                            </div>
                            <div className="summary-item">
                                <label>Estimated Killas:</label>
                                <span>{Math.floor(chakArea / gridConfig.killaSize)}</span>
                            </div>
                            <div className="summary-item">
                                <label>Area in Hectares:</label>
                                <span>{(chakArea * 0.4047).toFixed(2)} ha</span>
                            </div>
                            <div className="summary-item">
                                <label>Area in Square Feet:</label>
                                <span>{(chakArea * 43560).toFixed(0)} sq ft</span>
                            </div>
                        </div>
                        <div className="export-options">
                            <button onClick={() => exportChakData()} className="export-btn">
                                Export Chak Data
                            </button>
                            <button onClick={() => clearChakData()} className="clear-btn">
                                Clear Chak
                            </button>
                        </div>
                    </div>
                )}
            </div>
            <Footer />
        </div>
    );
}

export default App;