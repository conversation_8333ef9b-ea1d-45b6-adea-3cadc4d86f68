{"ast": null, "code": "import { useEffect, useRef } from 'react';\nimport { useLeafletContext } from './context.js';\nimport { useEventHandlers } from './events.js';\nimport { useLayerLifecycle } from './layer.js';\nimport { withPane } from './pane.js';\nexport function usePathOptions(element, props) {\n  const optionsRef = useRef();\n  useEffect(function updatePathOptions() {\n    if (props.pathOptions !== optionsRef.current) {\n      const options = props.pathOptions ?? {};\n      element.instance.setStyle(options);\n      optionsRef.current = options;\n    }\n  }, [element, props]);\n}\nexport function createPathHook(useElement) {\n  return function usePath(props) {\n    const context = useLeafletContext();\n    const elementRef = useElement(withPane(props, context), context);\n    useEventHandlers(elementRef.current, props.eventHandlers);\n    useLayerLifecycle(elementRef.current, context);\n    usePathOptions(elementRef.current, props);\n    return elementRef;\n  };\n}", "map": {"version": 3, "names": ["useEffect", "useRef", "useLeafletContext", "useEventHandlers", "useLayerLifecycle", "with<PERSON>ane", "usePathOptions", "element", "props", "optionsRef", "updatePathOptions", "pathOptions", "current", "options", "instance", "setStyle", "createPathHook", "useElement", "usePath", "context", "elementRef", "eventHandlers"], "sources": ["D:/Desktop files/My New Desktop/Irrigation Engineering and Structure Design/frontend/node_modules/@react-leaflet/core/lib/path.js"], "sourcesContent": ["import { useEffect, useRef } from 'react';\nimport { useLeafletContext } from './context.js';\nimport { useEventHandlers } from './events.js';\nimport { useLayerLifecycle } from './layer.js';\nimport { withPane } from './pane.js';\nexport function usePathOptions(element, props) {\n    const optionsRef = useRef();\n    useEffect(function updatePathOptions() {\n        if (props.pathOptions !== optionsRef.current) {\n            const options = props.pathOptions ?? {};\n            element.instance.setStyle(options);\n            optionsRef.current = options;\n        }\n    }, [\n        element,\n        props\n    ]);\n}\nexport function createPathHook(useElement) {\n    return function usePath(props) {\n        const context = useLeafletContext();\n        const elementRef = useElement(withPane(props, context), context);\n        useEventHandlers(elementRef.current, props.eventHandlers);\n        useLayerLifecycle(elementRef.current, context);\n        usePathOptions(elementRef.current, props);\n        return elementRef;\n    };\n}\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACzC,SAASC,iBAAiB,QAAQ,cAAc;AAChD,SAASC,gBAAgB,QAAQ,aAAa;AAC9C,SAASC,iBAAiB,QAAQ,YAAY;AAC9C,SAASC,QAAQ,QAAQ,WAAW;AACpC,OAAO,SAASC,cAAcA,CAACC,OAAO,EAAEC,KAAK,EAAE;EAC3C,MAAMC,UAAU,GAAGR,MAAM,CAAC,CAAC;EAC3BD,SAAS,CAAC,SAASU,iBAAiBA,CAAA,EAAG;IACnC,IAAIF,KAAK,CAACG,WAAW,KAAKF,UAAU,CAACG,OAAO,EAAE;MAC1C,MAAMC,OAAO,GAAGL,KAAK,CAACG,WAAW,IAAI,CAAC,CAAC;MACvCJ,OAAO,CAACO,QAAQ,CAACC,QAAQ,CAACF,OAAO,CAAC;MAClCJ,UAAU,CAACG,OAAO,GAAGC,OAAO;IAChC;EACJ,CAAC,EAAE,CACCN,OAAO,EACPC,KAAK,CACR,CAAC;AACN;AACA,OAAO,SAASQ,cAAcA,CAACC,UAAU,EAAE;EACvC,OAAO,SAASC,OAAOA,CAACV,KAAK,EAAE;IAC3B,MAAMW,OAAO,GAAGjB,iBAAiB,CAAC,CAAC;IACnC,MAAMkB,UAAU,GAAGH,UAAU,CAACZ,QAAQ,CAACG,KAAK,EAAEW,OAAO,CAAC,EAAEA,OAAO,CAAC;IAChEhB,gBAAgB,CAACiB,UAAU,CAACR,OAAO,EAAEJ,KAAK,CAACa,aAAa,CAAC;IACzDjB,iBAAiB,CAACgB,UAAU,CAACR,OAAO,EAAEO,OAAO,CAAC;IAC9Cb,cAAc,CAACc,UAAU,CAACR,OAAO,EAAEJ,KAAK,CAAC;IACzC,OAAOY,UAAU;EACrB,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}