{"ast": null, "code": "import { createElementObject, createLayerComponent, extendContext, updateMediaOverlay } from '@react-leaflet/core';\nimport { VideoOverlay as LeafletVideoOverlay } from 'leaflet';\nexport const VideoOverlay = createLayerComponent(function createVideoOverlay({\n  bounds,\n  url,\n  ...options\n}, ctx) {\n  const overlay = new LeafletVideoOverlay(url, bounds, options);\n  if (options.play === true) {\n    overlay.getElement()?.play();\n  }\n  return createElementObject(overlay, extendContext(ctx, {\n    overlayContainer: overlay\n  }));\n}, function updateVideoOverlay(overlay, props, prevProps) {\n  updateMediaOverlay(overlay, props, prevProps);\n  if (typeof props.url === 'string' && props.url !== prevProps.url) {\n    overlay.setUrl(props.url);\n  }\n  const video = overlay.getElement();\n  if (video != null) {\n    if (props.play === true && !prevProps.play) {\n      video.play();\n    } else if (!props.play && prevProps.play === true) {\n      video.pause();\n    }\n  }\n});", "map": {"version": 3, "names": ["createElementObject", "createLayerComponent", "extendContext", "updateMediaOverlay", "VideoOverlay", "LeafletVideoOverlay", "createVideoOverlay", "bounds", "url", "options", "ctx", "overlay", "play", "getElement", "overlayContainer", "updateVideoOverlay", "props", "prevProps", "setUrl", "video", "pause"], "sources": ["D:/Desktop files/My New Desktop/Irrigation Engineering and Structure Design/frontend/node_modules/react-leaflet/lib/VideoOverlay.js"], "sourcesContent": ["import { createElementObject, createLayerComponent, extendContext, updateMediaOverlay } from '@react-leaflet/core';\nimport { VideoOverlay as LeafletVideoOverlay } from 'leaflet';\nexport const VideoOverlay = createLayerComponent(function createVideoOverlay({ bounds , url , ...options }, ctx) {\n    const overlay = new LeafletVideoOverlay(url, bounds, options);\n    if (options.play === true) {\n        overlay.getElement()?.play();\n    }\n    return createElementObject(overlay, extendContext(ctx, {\n        overlayContainer: overlay\n    }));\n}, function updateVideoOverlay(overlay, props, prevProps) {\n    updateMediaOverlay(overlay, props, prevProps);\n    if (typeof props.url === 'string' && props.url !== prevProps.url) {\n        overlay.setUrl(props.url);\n    }\n    const video = overlay.getElement();\n    if (video != null) {\n        if (props.play === true && !prevProps.play) {\n            video.play();\n        } else if (!props.play && prevProps.play === true) {\n            video.pause();\n        }\n    }\n});\n"], "mappings": "AAAA,SAASA,mBAAmB,EAAEC,oBAAoB,EAAEC,aAAa,EAAEC,kBAAkB,QAAQ,qBAAqB;AAClH,SAASC,YAAY,IAAIC,mBAAmB,QAAQ,SAAS;AAC7D,OAAO,MAAMD,YAAY,GAAGH,oBAAoB,CAAC,SAASK,kBAAkBA,CAAC;EAAEC,MAAM;EAAGC,GAAG;EAAG,GAAGC;AAAQ,CAAC,EAAEC,GAAG,EAAE;EAC7G,MAAMC,OAAO,GAAG,IAAIN,mBAAmB,CAACG,GAAG,EAAED,MAAM,EAAEE,OAAO,CAAC;EAC7D,IAAIA,OAAO,CAACG,IAAI,KAAK,IAAI,EAAE;IACvBD,OAAO,CAACE,UAAU,CAAC,CAAC,EAAED,IAAI,CAAC,CAAC;EAChC;EACA,OAAOZ,mBAAmB,CAACW,OAAO,EAAET,aAAa,CAACQ,GAAG,EAAE;IACnDI,gBAAgB,EAAEH;EACtB,CAAC,CAAC,CAAC;AACP,CAAC,EAAE,SAASI,kBAAkBA,CAACJ,OAAO,EAAEK,KAAK,EAAEC,SAAS,EAAE;EACtDd,kBAAkB,CAACQ,OAAO,EAAEK,KAAK,EAAEC,SAAS,CAAC;EAC7C,IAAI,OAAOD,KAAK,CAACR,GAAG,KAAK,QAAQ,IAAIQ,KAAK,CAACR,GAAG,KAAKS,SAAS,CAACT,GAAG,EAAE;IAC9DG,OAAO,CAACO,MAAM,CAACF,KAAK,CAACR,GAAG,CAAC;EAC7B;EACA,MAAMW,KAAK,GAAGR,OAAO,CAACE,UAAU,CAAC,CAAC;EAClC,IAAIM,KAAK,IAAI,IAAI,EAAE;IACf,IAAIH,KAAK,CAACJ,IAAI,KAAK,IAAI,IAAI,CAACK,SAAS,CAACL,IAAI,EAAE;MACxCO,KAAK,CAACP,IAAI,CAAC,CAAC;IAChB,CAAC,MAAM,IAAI,CAACI,KAAK,CAACJ,IAAI,IAAIK,SAAS,CAACL,IAAI,KAAK,IAAI,EAAE;MAC/CO,KAAK,CAACC,KAAK,CAAC,CAAC;IACjB;EACJ;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}