{"name": "@turf/isolines", "version": "6.5.0", "description": "turf isolines module", "author": "Turf Authors", "contributors": ["<PERSON> <@stebogit>"], "license": "MIT", "bugs": {"url": "https://github.com/Turfjs/turf/issues"}, "homepage": "https://github.com/Turfjs/turf", "repository": {"type": "git", "url": "git://github.com/Turfjs/turf.git"}, "funding": "https://opencollective.com/turf", "publishConfig": {"access": "public"}, "keywords": ["turf", "g<PERSON><PERSON><PERSON>", "isolines", "contours", "elevation", "topography"], "main": "dist/js/index.js", "module": "dist/es/index.js", "exports": {"./package.json": "./package.json", ".": {"import": "./dist/es/index.js", "require": "./dist/js/index.js"}}, "types": "index.d.ts", "sideEffects": false, "files": ["dist", "index.d.ts"], "scripts": {"bench": "node -r esm bench.js", "build": "rollup -c ../../rollup.config.js && echo '{\"type\":\"module\"}' > dist/es/package.json", "docs": "node ../../scripts/generate-readmes", "test": "npm-run-all test:*", "test:tape": "node -r esm test.js", "test:types": "tsc --esModuleInterop --noEmit types.ts"}, "devDependencies": {"@turf/envelope": "^6.5.0", "@turf/point-grid": "^6.5.0", "@turf/random": "^6.5.0", "@turf/rhumb-destination": "^6.5.0", "@turf/truncate": "^6.5.0", "benchmark": "*", "load-json-file": "*", "matrix-to-grid": "*", "npm-run-all": "*", "rollup": "*", "tape": "*", "write-json-file": "*"}, "dependencies": {"@turf/bbox": "^6.5.0", "@turf/helpers": "^6.5.0", "@turf/invariant": "^6.5.0", "@turf/meta": "^6.5.0", "object-assign": "*"}, "gitHead": "5375941072b90d489389db22b43bfe809d5e451e"}