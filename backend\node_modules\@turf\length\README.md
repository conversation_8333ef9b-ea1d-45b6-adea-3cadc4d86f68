# @turf/length

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## length

Takes a [GeoJSON][1] and measures its length in the specified units, [(Multi)Point][2]'s distance are ignored.

**Parameters**

-   `geojson` **[Feature][3]&lt;([LineString][4] \| [MultiLineString][5])>** GeoJSON to measure
-   `options` **[Object][6]** Optional parameters (optional, default `{}`)
    -   `options.units` **[string][7]** can be degrees, radians, miles, or kilometers (optional, default `kilometers`)

**Examples**

```javascript
var line = turf.lineString([[115, -32], [131, -22], [143, -25], [150, -34]]);
var length = turf.length(line, {units: 'miles'});

//addToMap
var addToMap = [line];
line.properties.distance = length;
```

Returns **[number][8]** length of GeoJSON

[1]: https://tools.ietf.org/html/rfc7946#section-3

[2]: https://tools.ietf.org/html/rfc7946#section-3.1.2

[3]: https://tools.ietf.org/html/rfc7946#section-3.2

[4]: https://tools.ietf.org/html/rfc7946#section-3.1.4

[5]: https://tools.ietf.org/html/rfc7946#section-3.1.5

[6]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[7]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String

[8]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number

<!-- This file is automatically generated. Please don't edit it directly:
if you find an error, edit the source file (likely index.js), and re-run
./scripts/generate-readmes in the turf project. -->

---

This module is part of the [Turfjs project](http://turfjs.org/), an open source
module collection dedicated to geographic algorithms. It is maintained in the
[Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create
PRs and issues.

### Installation

Install this module individually:

```sh
$ npm install @turf/length
```

Or install the Turf module that includes it as a function:

```sh
$ npm install @turf/turf
```
