{"ast": null, "code": "import { LeafletProvider, createC<PERSON>r<PERSON>omponent, create<PERSON><PERSON><PERSON>H<PERSON>, createE<PERSON>Hook, createElementObject, extendContext, useLeafletContext } from '@react-leaflet/core';\nimport { Control } from 'leaflet';\nimport React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';\nexport const useLayersControlElement = createElementHook(function createLayersControl({\n  children: _c,\n  ...options\n}, ctx) {\n  const control = new Control.Layers(undefined, undefined, options);\n  return createElementObject(control, extendContext(ctx, {\n    layersControl: control\n  }));\n}, function updateLayersControl(control, props, prevProps) {\n  if (props.collapsed !== prevProps.collapsed) {\n    if (props.collapsed === true) {\n      control.collapse();\n    } else {\n      control.expand();\n    }\n  }\n});\nexport const useLayersControl = createControlHook(useLayersControlElement);\n// @ts-ignore\nexport const LayersControl = createContainerComponent(useLayersControl);\nexport function createControlledLayer(addLayerToControl) {\n  return function ControlledLayer(props) {\n    const parentContext = useLeafletContext();\n    const propsRef = useRef(props);\n    const [layer, setLayer] = useState(null);\n    const {\n      layersControl,\n      map\n    } = parentContext;\n    const addLayer = useCallback(layerToAdd => {\n      if (layersControl != null) {\n        if (propsRef.current.checked) {\n          map.addLayer(layerToAdd);\n        }\n        addLayerToControl(layersControl, layerToAdd, propsRef.current.name);\n        setLayer(layerToAdd);\n      }\n    }, [layersControl, map]);\n    const removeLayer = useCallback(layerToRemove => {\n      layersControl?.removeLayer(layerToRemove);\n      setLayer(null);\n    }, [layersControl]);\n    const context = useMemo(() => {\n      return extendContext(parentContext, {\n        layerContainer: {\n          addLayer,\n          removeLayer\n        }\n      });\n    }, [parentContext, addLayer, removeLayer]);\n    useEffect(() => {\n      if (layer !== null && propsRef.current !== props) {\n        if (props.checked === true && (propsRef.current.checked == null || propsRef.current.checked === false)) {\n          map.addLayer(layer);\n        } else if (propsRef.current.checked === true && (props.checked == null || props.checked === false)) {\n          map.removeLayer(layer);\n        }\n        propsRef.current = props;\n      }\n    });\n    return props.children ? /*#__PURE__*/React.createElement(LeafletProvider, {\n      value: context\n    }, props.children) : null;\n  };\n}\nLayersControl.BaseLayer = createControlledLayer(function addBaseLayer(layersControl, layer, name) {\n  layersControl.addBaseLayer(layer, name);\n});\nLayersControl.Overlay = createControlledLayer(function addOverlay(layersControl, layer, name) {\n  layersControl.addOverlay(layer, name);\n});", "map": {"version": 3, "names": ["LeafletProvider", "createContainerComponent", "createControlHook", "createElementHook", "createElementObject", "extendContext", "useLeafletContext", "Control", "React", "useCallback", "useEffect", "useMemo", "useRef", "useState", "useLayersControlElement", "createLayersControl", "children", "_c", "options", "ctx", "control", "Layers", "undefined", "layersControl", "updateLayersControl", "props", "prevProps", "collapsed", "collapse", "expand", "useLayersControl", "LayersControl", "createControlledLayer", "addLayerToControl", "ControlledLayer", "parentContext", "propsRef", "layer", "<PERSON><PERSON><PERSON><PERSON>", "map", "add<PERSON><PERSON>er", "layerToAdd", "current", "checked", "name", "<PERSON><PERSON><PERSON>er", "layerToRemove", "context", "layerContainer", "createElement", "value", "Base<PERSON><PERSON>er", "addBaseLayer", "Overlay", "addOverlay"], "sources": ["D:/Desktop files/My New Desktop/Irrigation Engineering and Structure Design/frontend/node_modules/react-leaflet/lib/LayersControl.js"], "sourcesContent": ["import { LeafletProvider, createC<PERSON>r<PERSON>omponent, create<PERSON><PERSON><PERSON>H<PERSON>, createE<PERSON>Hook, createElementObject, extendContext, useLeafletContext } from '@react-leaflet/core';\nimport { Control } from 'leaflet';\nimport React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';\nexport const useLayersControlElement = createElementHook(function createLayersControl({ children: _c , ...options }, ctx) {\n    const control = new Control.Layers(undefined, undefined, options);\n    return createElementObject(control, extendContext(ctx, {\n        layersControl: control\n    }));\n}, function updateLayersControl(control, props, prevProps) {\n    if (props.collapsed !== prevProps.collapsed) {\n        if (props.collapsed === true) {\n            control.collapse();\n        } else {\n            control.expand();\n        }\n    }\n});\nexport const useLayersControl = createControlHook(useLayersControlElement);\n// @ts-ignore\nexport const LayersControl = createContainerComponent(useLayersControl);\nexport function createControlledLayer(addLayerToControl) {\n    return function ControlledLayer(props) {\n        const parentContext = useLeafletContext();\n        const propsRef = useRef(props);\n        const [layer, setLayer] = useState(null);\n        const { layersControl , map  } = parentContext;\n        const addLayer = useCallback((layerToAdd)=>{\n            if (layersControl != null) {\n                if (propsRef.current.checked) {\n                    map.addLayer(layerToAdd);\n                }\n                addLayerToControl(layersControl, layerToAdd, propsRef.current.name);\n                setLayer(layerToAdd);\n            }\n        }, [\n            layersControl,\n            map\n        ]);\n        const removeLayer = useCallback((layerToRemove)=>{\n            layersControl?.removeLayer(layerToRemove);\n            setLayer(null);\n        }, [\n            layersControl\n        ]);\n        const context = useMemo(()=>{\n            return extendContext(parentContext, {\n                layerContainer: {\n                    addLayer,\n                    removeLayer\n                }\n            });\n        }, [\n            parentContext,\n            addLayer,\n            removeLayer\n        ]);\n        useEffect(()=>{\n            if (layer !== null && propsRef.current !== props) {\n                if (props.checked === true && (propsRef.current.checked == null || propsRef.current.checked === false)) {\n                    map.addLayer(layer);\n                } else if (propsRef.current.checked === true && (props.checked == null || props.checked === false)) {\n                    map.removeLayer(layer);\n                }\n                propsRef.current = props;\n            }\n        });\n        return props.children ? /*#__PURE__*/ React.createElement(LeafletProvider, {\n            value: context\n        }, props.children) : null;\n    };\n}\nLayersControl.BaseLayer = createControlledLayer(function addBaseLayer(layersControl, layer, name) {\n    layersControl.addBaseLayer(layer, name);\n});\nLayersControl.Overlay = createControlledLayer(function addOverlay(layersControl, layer, name) {\n    layersControl.addOverlay(layer, name);\n});\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,wBAAwB,EAAEC,iBAAiB,EAAEC,iBAAiB,EAAEC,mBAAmB,EAAEC,aAAa,EAAEC,iBAAiB,QAAQ,qBAAqB;AAC5K,SAASC,OAAO,QAAQ,SAAS;AACjC,OAAOC,KAAK,IAAIC,WAAW,EAAEC,SAAS,EAAEC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAChF,OAAO,MAAMC,uBAAuB,GAAGX,iBAAiB,CAAC,SAASY,mBAAmBA,CAAC;EAAEC,QAAQ,EAAEC,EAAE;EAAG,GAAGC;AAAQ,CAAC,EAAEC,GAAG,EAAE;EACtH,MAAMC,OAAO,GAAG,IAAIb,OAAO,CAACc,MAAM,CAACC,SAAS,EAAEA,SAAS,EAAEJ,OAAO,CAAC;EACjE,OAAOd,mBAAmB,CAACgB,OAAO,EAAEf,aAAa,CAACc,GAAG,EAAE;IACnDI,aAAa,EAAEH;EACnB,CAAC,CAAC,CAAC;AACP,CAAC,EAAE,SAASI,mBAAmBA,CAACJ,OAAO,EAAEK,KAAK,EAAEC,SAAS,EAAE;EACvD,IAAID,KAAK,CAACE,SAAS,KAAKD,SAAS,CAACC,SAAS,EAAE;IACzC,IAAIF,KAAK,CAACE,SAAS,KAAK,IAAI,EAAE;MAC1BP,OAAO,CAACQ,QAAQ,CAAC,CAAC;IACtB,CAAC,MAAM;MACHR,OAAO,CAACS,MAAM,CAAC,CAAC;IACpB;EACJ;AACJ,CAAC,CAAC;AACF,OAAO,MAAMC,gBAAgB,GAAG5B,iBAAiB,CAACY,uBAAuB,CAAC;AAC1E;AACA,OAAO,MAAMiB,aAAa,GAAG9B,wBAAwB,CAAC6B,gBAAgB,CAAC;AACvE,OAAO,SAASE,qBAAqBA,CAACC,iBAAiB,EAAE;EACrD,OAAO,SAASC,eAAeA,CAACT,KAAK,EAAE;IACnC,MAAMU,aAAa,GAAG7B,iBAAiB,CAAC,CAAC;IACzC,MAAM8B,QAAQ,GAAGxB,MAAM,CAACa,KAAK,CAAC;IAC9B,MAAM,CAACY,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;IACxC,MAAM;MAAEU,aAAa;MAAGgB;IAAK,CAAC,GAAGJ,aAAa;IAC9C,MAAMK,QAAQ,GAAG/B,WAAW,CAAEgC,UAAU,IAAG;MACvC,IAAIlB,aAAa,IAAI,IAAI,EAAE;QACvB,IAAIa,QAAQ,CAACM,OAAO,CAACC,OAAO,EAAE;UAC1BJ,GAAG,CAACC,QAAQ,CAACC,UAAU,CAAC;QAC5B;QACAR,iBAAiB,CAACV,aAAa,EAAEkB,UAAU,EAAEL,QAAQ,CAACM,OAAO,CAACE,IAAI,CAAC;QACnEN,QAAQ,CAACG,UAAU,CAAC;MACxB;IACJ,CAAC,EAAE,CACClB,aAAa,EACbgB,GAAG,CACN,CAAC;IACF,MAAMM,WAAW,GAAGpC,WAAW,CAAEqC,aAAa,IAAG;MAC7CvB,aAAa,EAAEsB,WAAW,CAACC,aAAa,CAAC;MACzCR,QAAQ,CAAC,IAAI,CAAC;IAClB,CAAC,EAAE,CACCf,aAAa,CAChB,CAAC;IACF,MAAMwB,OAAO,GAAGpC,OAAO,CAAC,MAAI;MACxB,OAAON,aAAa,CAAC8B,aAAa,EAAE;QAChCa,cAAc,EAAE;UACZR,QAAQ;UACRK;QACJ;MACJ,CAAC,CAAC;IACN,CAAC,EAAE,CACCV,aAAa,EACbK,QAAQ,EACRK,WAAW,CACd,CAAC;IACFnC,SAAS,CAAC,MAAI;MACV,IAAI2B,KAAK,KAAK,IAAI,IAAID,QAAQ,CAACM,OAAO,KAAKjB,KAAK,EAAE;QAC9C,IAAIA,KAAK,CAACkB,OAAO,KAAK,IAAI,KAAKP,QAAQ,CAACM,OAAO,CAACC,OAAO,IAAI,IAAI,IAAIP,QAAQ,CAACM,OAAO,CAACC,OAAO,KAAK,KAAK,CAAC,EAAE;UACpGJ,GAAG,CAACC,QAAQ,CAACH,KAAK,CAAC;QACvB,CAAC,MAAM,IAAID,QAAQ,CAACM,OAAO,CAACC,OAAO,KAAK,IAAI,KAAKlB,KAAK,CAACkB,OAAO,IAAI,IAAI,IAAIlB,KAAK,CAACkB,OAAO,KAAK,KAAK,CAAC,EAAE;UAChGJ,GAAG,CAACM,WAAW,CAACR,KAAK,CAAC;QAC1B;QACAD,QAAQ,CAACM,OAAO,GAAGjB,KAAK;MAC5B;IACJ,CAAC,CAAC;IACF,OAAOA,KAAK,CAACT,QAAQ,GAAG,aAAcR,KAAK,CAACyC,aAAa,CAACjD,eAAe,EAAE;MACvEkD,KAAK,EAAEH;IACX,CAAC,EAAEtB,KAAK,CAACT,QAAQ,CAAC,GAAG,IAAI;EAC7B,CAAC;AACL;AACAe,aAAa,CAACoB,SAAS,GAAGnB,qBAAqB,CAAC,SAASoB,YAAYA,CAAC7B,aAAa,EAAEc,KAAK,EAAEO,IAAI,EAAE;EAC9FrB,aAAa,CAAC6B,YAAY,CAACf,KAAK,EAAEO,IAAI,CAAC;AAC3C,CAAC,CAAC;AACFb,aAAa,CAACsB,OAAO,GAAGrB,qBAAqB,CAAC,SAASsB,UAAUA,CAAC/B,aAAa,EAAEc,KAAK,EAAEO,IAAI,EAAE;EAC1FrB,aAAa,CAAC+B,UAAU,CAACjB,KAAK,EAAEO,IAAI,CAAC;AACzC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}