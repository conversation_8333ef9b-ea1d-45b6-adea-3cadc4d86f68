# @turf/clone

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## clone

Returns a cloned copy of the passed GeoJSON Object, including possible 'Foreign Members'.
~3-5x faster than the common JSON.parse + JSON.stringify combo method.

**Parameters**

-   `geo<PERSON><PERSON>` **[GeoJSON][1]** GeoJSON Object

**Examples**

```javascript
var line = turf.lineString([[-74, 40], [-78, 42], [-82, 35]], {color: 'red'});

var lineCloned = turf.clone(line);
```

Returns **[GeoJSON][1]** cloned Geo<PERSON><PERSON><PERSON> Object

[1]: https://tools.ietf.org/html/rfc7946#section-3

<!-- This file is automatically generated. Please don't edit it directly:
if you find an error, edit the source file (likely index.js), and re-run
./scripts/generate-readmes in the turf project. -->

---

This module is part of the [Turfjs project](http://turfjs.org/), an open source
module collection dedicated to geographic algorithms. It is maintained in the
[Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create
PRs and issues.

### Installation

Install this module individually:

```sh
$ npm install @turf/clone
```

Or install the Turf module that includes it as a function:

```sh
$ npm install @turf/turf
```
