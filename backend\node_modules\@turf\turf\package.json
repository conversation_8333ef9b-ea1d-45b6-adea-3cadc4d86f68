{"name": "@turf/turf", "version": "6.5.0", "description": "a JavaScript library for performing geospatial operations with GeoJSON", "author": "Turf Authors", "license": "MIT", "bugs": {"url": "https://github.com/Turfjs/turf/issues"}, "homepage": "https://github.com/Turfjs/turf", "repository": {"type": "git", "url": "git://github.com/Turfjs/turf.git"}, "funding": "https://opencollective.com/turf", "publishConfig": {"access": "public"}, "keywords": ["gis", "geo", "geojs", "geospatial", "geography", "geometry", "map", "contour", "centroid", "tin", "extent", "g<PERSON><PERSON><PERSON>", "grid", "polygon", "line", "point", "area", "analysis", "statistics", "stats", "midpoint", "plane", "quantile", "jenks", "sample"], "main": "dist/js/index.js", "module": "dist/es/index.js", "exports": {"./package.json": "./package.json", ".": {"import": "./dist/es/index.js", "require": "./dist/js/index.js"}}, "browser": "turf.min.js", "types": "index.d.ts", "sideEffects": false, "files": ["dist", "index.d.ts", "turf.min.js"], "scripts": {"build": "rollup -c rollup.config.js && echo '{\"type\":\"module\"}' > dist/es/package.json", "last-checks": "npm-run-all last-checks:testjs last-checks:example", "last-checks:example": "node test.example.js", "last-checks:testjs": "node test.js", "test": "echo '@turf/turf tests run in the last-checks step'"}, "devDependencies": {"@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "camelcase": "*", "documentation": "^13.2.5", "glob": "*", "rollup": "^2.34.2", "rollup-plugin-terser": "^7.0.2", "tape": "*"}, "dependencies": {"@turf/along": "^6.5.0", "@turf/angle": "^6.5.0", "@turf/area": "^6.5.0", "@turf/bbox": "^6.5.0", "@turf/bbox-clip": "^6.5.0", "@turf/bbox-polygon": "^6.5.0", "@turf/bearing": "^6.5.0", "@turf/bezier-spline": "^6.5.0", "@turf/boolean-clockwise": "^6.5.0", "@turf/boolean-contains": "^6.5.0", "@turf/boolean-crosses": "^6.5.0", "@turf/boolean-disjoint": "^6.5.0", "@turf/boolean-equal": "^6.5.0", "@turf/boolean-intersects": "^6.5.0", "@turf/boolean-overlap": "^6.5.0", "@turf/boolean-parallel": "^6.5.0", "@turf/boolean-point-in-polygon": "^6.5.0", "@turf/boolean-point-on-line": "^6.5.0", "@turf/boolean-within": "^6.5.0", "@turf/buffer": "^6.5.0", "@turf/center": "^6.5.0", "@turf/center-mean": "^6.5.0", "@turf/center-median": "^6.5.0", "@turf/center-of-mass": "^6.5.0", "@turf/centroid": "^6.5.0", "@turf/circle": "^6.5.0", "@turf/clean-coords": "^6.5.0", "@turf/clone": "^6.5.0", "@turf/clusters": "^6.5.0", "@turf/clusters-dbscan": "^6.5.0", "@turf/clusters-kmeans": "^6.5.0", "@turf/collect": "^6.5.0", "@turf/combine": "^6.5.0", "@turf/concave": "^6.5.0", "@turf/convex": "^6.5.0", "@turf/destination": "^6.5.0", "@turf/difference": "^6.5.0", "@turf/dissolve": "^6.5.0", "@turf/distance": "^6.5.0", "@turf/distance-weight": "^6.5.0", "@turf/ellipse": "^6.5.0", "@turf/envelope": "^6.5.0", "@turf/explode": "^6.5.0", "@turf/flatten": "^6.5.0", "@turf/flip": "^6.5.0", "@turf/great-circle": "^6.5.0", "@turf/helpers": "^6.5.0", "@turf/hex-grid": "^6.5.0", "@turf/interpolate": "^6.5.0", "@turf/intersect": "^6.5.0", "@turf/invariant": "^6.5.0", "@turf/isobands": "^6.5.0", "@turf/isolines": "^6.5.0", "@turf/kinks": "^6.5.0", "@turf/length": "^6.5.0", "@turf/line-arc": "^6.5.0", "@turf/line-chunk": "^6.5.0", "@turf/line-intersect": "^6.5.0", "@turf/line-offset": "^6.5.0", "@turf/line-overlap": "^6.5.0", "@turf/line-segment": "^6.5.0", "@turf/line-slice": "^6.5.0", "@turf/line-slice-along": "^6.5.0", "@turf/line-split": "^6.5.0", "@turf/line-to-polygon": "^6.5.0", "@turf/mask": "^6.5.0", "@turf/meta": "^6.5.0", "@turf/midpoint": "^6.5.0", "@turf/moran-index": "^6.5.0", "@turf/nearest-point": "^6.5.0", "@turf/nearest-point-on-line": "^6.5.0", "@turf/nearest-point-to-line": "^6.5.0", "@turf/planepoint": "^6.5.0", "@turf/point-grid": "^6.5.0", "@turf/point-on-feature": "^6.5.0", "@turf/point-to-line-distance": "^6.5.0", "@turf/points-within-polygon": "^6.5.0", "@turf/polygon-smooth": "^6.5.0", "@turf/polygon-tangents": "^6.5.0", "@turf/polygon-to-line": "^6.5.0", "@turf/polygonize": "^6.5.0", "@turf/projection": "^6.5.0", "@turf/random": "^6.5.0", "@turf/rewind": "^6.5.0", "@turf/rhumb-bearing": "^6.5.0", "@turf/rhumb-destination": "^6.5.0", "@turf/rhumb-distance": "^6.5.0", "@turf/sample": "^6.5.0", "@turf/sector": "^6.5.0", "@turf/shortest-path": "^6.5.0", "@turf/simplify": "^6.5.0", "@turf/square": "^6.5.0", "@turf/square-grid": "^6.5.0", "@turf/standard-deviational-ellipse": "^6.5.0", "@turf/tag": "^6.5.0", "@turf/tesselate": "^6.5.0", "@turf/tin": "^6.5.0", "@turf/transform-rotate": "^6.5.0", "@turf/transform-scale": "^6.5.0", "@turf/transform-translate": "^6.5.0", "@turf/triangle-grid": "^6.5.0", "@turf/truncate": "^6.5.0", "@turf/union": "^6.5.0", "@turf/unkink-polygon": "^6.5.0", "@turf/voronoi": "^6.5.0"}, "gitHead": "5375941072b90d489389db22b43bfe809d5e451e"}