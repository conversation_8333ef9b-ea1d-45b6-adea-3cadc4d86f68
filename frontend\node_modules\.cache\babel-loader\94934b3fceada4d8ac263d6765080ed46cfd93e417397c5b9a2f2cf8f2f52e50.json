{"ast": null, "code": "import { useLeafletContext } from '@react-leaflet/core';\nimport { useEffect } from 'react';\nexport function useMap() {\n  return useLeafletContext().map;\n}\nexport function useMapEvent(type, handler) {\n  const map = useMap();\n  useEffect(function addMapEventHandler() {\n    // @ts-ignore event type\n    map.on(type, handler);\n    return function removeMapEventHandler() {\n      // @ts-ignore event type\n      map.off(type, handler);\n    };\n  }, [map, type, handler]);\n  return map;\n}\nexport function useMapEvents(handlers) {\n  const map = useMap();\n  useEffect(function addMapEventHandlers() {\n    map.on(handlers);\n    return function removeMapEventHandlers() {\n      map.off(handlers);\n    };\n  }, [map, handlers]);\n  return map;\n}", "map": {"version": 3, "names": ["useLeafletContext", "useEffect", "useMap", "map", "useMapEvent", "type", "handler", "addMapEventHandler", "on", "removeMapEventHandler", "off", "useMapEvents", "handlers", "addMapEventHandlers", "removeMapEventHandlers"], "sources": ["D:/Desktop files/My New Desktop/Irrigation Engineering and Structure Design/frontend/node_modules/react-leaflet/lib/hooks.js"], "sourcesContent": ["import { useLeafletContext } from '@react-leaflet/core';\nimport { useEffect } from 'react';\nexport function useMap() {\n    return useLeafletContext().map;\n}\nexport function useMapEvent(type, handler) {\n    const map = useMap();\n    useEffect(function addMapEventHandler() {\n        // @ts-ignore event type\n        map.on(type, handler);\n        return function removeMapEventHandler() {\n            // @ts-ignore event type\n            map.off(type, handler);\n        };\n    }, [\n        map,\n        type,\n        handler\n    ]);\n    return map;\n}\nexport function useMapEvents(handlers) {\n    const map = useMap();\n    useEffect(function addMapEventHandlers() {\n        map.on(handlers);\n        return function removeMapEventHandlers() {\n            map.off(handlers);\n        };\n    }, [\n        map,\n        handlers\n    ]);\n    return map;\n}\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,SAAS,QAAQ,OAAO;AACjC,OAAO,SAASC,MAAMA,CAAA,EAAG;EACrB,OAAOF,iBAAiB,CAAC,CAAC,CAACG,GAAG;AAClC;AACA,OAAO,SAASC,WAAWA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACvC,MAAMH,GAAG,GAAGD,MAAM,CAAC,CAAC;EACpBD,SAAS,CAAC,SAASM,kBAAkBA,CAAA,EAAG;IACpC;IACAJ,GAAG,CAACK,EAAE,CAACH,IAAI,EAAEC,OAAO,CAAC;IACrB,OAAO,SAASG,qBAAqBA,CAAA,EAAG;MACpC;MACAN,GAAG,CAACO,GAAG,CAACL,IAAI,EAAEC,OAAO,CAAC;IAC1B,CAAC;EACL,CAAC,EAAE,CACCH,GAAG,EACHE,IAAI,EACJC,OAAO,CACV,CAAC;EACF,OAAOH,GAAG;AACd;AACA,OAAO,SAASQ,YAAYA,CAACC,QAAQ,EAAE;EACnC,MAAMT,GAAG,GAAGD,MAAM,CAAC,CAAC;EACpBD,SAAS,CAAC,SAASY,mBAAmBA,CAAA,EAAG;IACrCV,GAAG,CAACK,EAAE,CAACI,QAAQ,CAAC;IAChB,OAAO,SAASE,sBAAsBA,CAAA,EAAG;MACrCX,GAAG,CAACO,GAAG,CAACE,QAAQ,CAAC;IACrB,CAAC;EACL,CAAC,EAAE,CACCT,GAAG,EACHS,QAAQ,CACX,CAAC;EACF,OAAOT,GAAG;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}