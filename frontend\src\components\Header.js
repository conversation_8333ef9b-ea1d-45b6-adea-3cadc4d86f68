import React from 'react';
import './Header.css';

function Header() {
  return (
    <header className="app-header">
      <div className="header-content">
        <div className="logo-section">
          <h1>🏗️ Irrigation Engineering Portal</h1>
          <p>Complete Solution for Irrigation Department Engineers</p>
        </div>
        <nav className="navigation">
          <div className="nav-item active">
            <span>📐 Chak Plan Designer</span>
          </div>
          <div className="nav-item">
            <span>🌊 Canal Design (Coming Soon)</span>
          </div>
          <div className="nav-item">
            <span>⚡ Outlet Design (Coming Soon)</span>
          </div>
          <div className="nav-item">
            <span>🏗️ Hydraulic Structures (Coming Soon)</span>
          </div>
        </nav>
      </div>
      <div className="header-info">
        <div className="info-card">
          <h3>About Chak Plan</h3>
          <p>
            A Chak Plan (also called Sazra Plan) is a systematic layout of agricultural land 
            divided into Murabas and Killas for efficient irrigation management. This tool 
            helps engineers design and calculate optimal land distribution patterns.
          </p>
        </div>
        <div className="info-card">
          <h3>Key Features</h3>
          <ul>
            <li>Interactive boundary drawing</li>
            <li>Automatic area calculations</li>
            <li>Grid generation with custom parameters</li>
            <li>Canal network visualization</li>
            <li>Export functionality for documentation</li>
          </ul>
        </div>
        <div className="info-card">
          <h3>Standard Units</h3>
          <ul>
            <li><strong>Muraba:</strong> 25 acres (5×5 acres)</li>
            <li><strong>Killa:</strong> 1 acre (220×198 feet)</li>
            <li><strong>Standard Grid:</strong> 220 ft E-W × 198 ft N-S</li>
          </ul>
        </div>
      </div>
    </header>
  );
}

export default Header;
