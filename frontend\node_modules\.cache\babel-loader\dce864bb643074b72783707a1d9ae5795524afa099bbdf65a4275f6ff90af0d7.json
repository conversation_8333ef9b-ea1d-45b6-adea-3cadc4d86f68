{"ast": null, "code": "import { useEffect, useRef } from 'react';\nexport function useEventHandlers(element, eventHandlers) {\n  const eventHandlersRef = useRef();\n  useEffect(function addEventHandlers() {\n    if (eventHandlers != null) {\n      element.instance.on(eventHandlers);\n    }\n    eventHandlersRef.current = eventHandlers;\n    return function removeEventHandlers() {\n      if (eventHandlersRef.current != null) {\n        element.instance.off(eventHandlersRef.current);\n      }\n      eventHandlersRef.current = null;\n    };\n  }, [element, eventHandlers]);\n}", "map": {"version": 3, "names": ["useEffect", "useRef", "useEventHandlers", "element", "eventHandlers", "eventHandlersRef", "addEventHandlers", "instance", "on", "current", "removeEventHandlers", "off"], "sources": ["D:/Desktop files/My New Desktop/Irrigation Engineering and Structure Design/frontend/node_modules/@react-leaflet/core/lib/events.js"], "sourcesContent": ["import { useEffect, useRef } from 'react';\nexport function useEventHandlers(element, eventHandlers) {\n    const eventHandlersRef = useRef();\n    useEffect(function addEventHandlers() {\n        if (eventHandlers != null) {\n            element.instance.on(eventHandlers);\n        }\n        eventHandlersRef.current = eventHandlers;\n        return function removeEventHandlers() {\n            if (eventHandlersRef.current != null) {\n                element.instance.off(eventHandlersRef.current);\n            }\n            eventHandlersRef.current = null;\n        };\n    }, [\n        element,\n        eventHandlers\n    ]);\n}\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACzC,OAAO,SAASC,gBAAgBA,CAACC,OAAO,EAAEC,aAAa,EAAE;EACrD,MAAMC,gBAAgB,GAAGJ,MAAM,CAAC,CAAC;EACjCD,SAAS,CAAC,SAASM,gBAAgBA,CAAA,EAAG;IAClC,IAAIF,aAAa,IAAI,IAAI,EAAE;MACvBD,OAAO,CAACI,QAAQ,CAACC,EAAE,CAACJ,aAAa,CAAC;IACtC;IACAC,gBAAgB,CAACI,OAAO,GAAGL,aAAa;IACxC,OAAO,SAASM,mBAAmBA,CAAA,EAAG;MAClC,IAAIL,gBAAgB,CAACI,OAAO,IAAI,IAAI,EAAE;QAClCN,OAAO,CAACI,QAAQ,CAACI,GAAG,CAACN,gBAAgB,CAACI,OAAO,CAAC;MAClD;MACAJ,gBAAgB,CAACI,OAAO,GAAG,IAAI;IACnC,CAAC;EACL,CAAC,EAAE,CACCN,OAAO,EACPC,aAAa,CAChB,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}