{"ast": null, "code": "import { createControlComponent } from '@react-leaflet/core';\nimport { Control } from 'leaflet';\nexport const ZoomControl = createControlComponent(function createZoomControl(props) {\n  return new Control.Zoom(props);\n});", "map": {"version": 3, "names": ["createControlComponent", "Control", "ZoomControl", "createZoomControl", "props", "Zoom"], "sources": ["D:/Desktop files/My New Desktop/Irrigation Engineering and Structure Design/frontend/node_modules/react-leaflet/lib/ZoomControl.js"], "sourcesContent": ["import { createControlComponent } from '@react-leaflet/core';\nimport { Control } from 'leaflet';\nexport const ZoomControl = createControlComponent(function createZoomControl(props) {\n    return new Control.Zoom(props);\n});\n"], "mappings": "AAAA,SAASA,sBAAsB,QAAQ,qBAAqB;AAC5D,SAASC,OAAO,QAAQ,SAAS;AACjC,OAAO,MAAMC,WAAW,GAAGF,sBAAsB,CAAC,SAASG,iBAAiBA,CAACC,KAAK,EAAE;EAChF,OAAO,IAAIH,OAAO,CAACI,IAAI,CAACD,KAAK,CAAC;AAClC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}