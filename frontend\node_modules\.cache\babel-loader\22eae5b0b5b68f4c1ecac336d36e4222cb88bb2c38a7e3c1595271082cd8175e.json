{"ast": null, "code": "var _jsxFileName = \"D:\\\\Desktop files\\\\My New Desktop\\\\Irrigation Engineering and Structure Design\\\\frontend\\\\src\\\\components\\\\Footer.js\";\nimport React from 'react';\nimport './Footer.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Footer() {\n  return /*#__PURE__*/_jsxDEV(\"footer\", {\n    className: \"app-footer\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"footer-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"\\uD83C\\uDFD7\\uFE0F Irrigation Engineering Portal\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 9,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Developed for irrigation department engineers and professionals working in water management and agricultural planning.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"\\uD83D\\uDCDA Resources\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Irrigation Engineering Handbook\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 15,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Canal Design Guidelines\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 16,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Chak Plan Standards\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Hydraulic Structure Manuals\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"\\uD83D\\uDD27 Tools Available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2705 Chak Plan Designer\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\uD83D\\uDD04 Canal Network Mapping\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u23F3 Outlet Design Calculator (Coming Soon)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u23F3 Hydraulic Structure Design (Coming Soon)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"\\uD83D\\uDCDE Support\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"For technical support and feature requests, please contact your system administrator.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"version\",\n          children: \"Version 1.0.0 - Beta\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"footer-bottom\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"\\xA9 2024 Irrigation Engineering Portal. Built for engineering excellence.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n}\n_c = Footer;\nexport default Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Footer", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Desktop files/My New Desktop/Irrigation Engineering and Structure Design/frontend/src/components/Footer.js"], "sourcesContent": ["import React from 'react';\nimport './Footer.css';\n\nfunction Footer() {\n  return (\n    <footer className=\"app-footer\">\n      <div className=\"footer-content\">\n        <div className=\"footer-section\">\n          <h4>🏗️ Irrigation Engineering Portal</h4>\n          <p>Developed for irrigation department engineers and professionals working in water management and agricultural planning.</p>\n        </div>\n        <div className=\"footer-section\">\n          <h4>📚 Resources</h4>\n          <ul>\n            <li>Irrigation Engineering Handbook</li>\n            <li>Canal Design Guidelines</li>\n            <li>Chak Plan Standards</li>\n            <li>Hydraulic Structure Manuals</li>\n          </ul>\n        </div>\n        <div className=\"footer-section\">\n          <h4>🔧 Tools Available</h4>\n          <ul>\n            <li>✅ Chak Plan Designer</li>\n            <li>🔄 Canal Network Mapping</li>\n            <li>⏳ Outlet Design Calculator (Coming Soon)</li>\n            <li>⏳ Hydraulic Structure Design (Coming Soon)</li>\n          </ul>\n        </div>\n        <div className=\"footer-section\">\n          <h4>📞 Support</h4>\n          <p>For technical support and feature requests, please contact your system administrator.</p>\n          <p className=\"version\">Version 1.0.0 - Beta</p>\n        </div>\n      </div>\n      <div className=\"footer-bottom\">\n        <p>&copy; 2024 Irrigation Engineering Portal. Built for engineering excellence.</p>\n      </div>\n    </footer>\n  );\n}\n\nexport default Footer;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,SAASC,MAAMA,CAAA,EAAG;EAChB,oBACED,OAAA;IAAQE,SAAS,EAAC,YAAY;IAAAC,QAAA,gBAC5BH,OAAA;MAAKE,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BH,OAAA;QAAKE,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BH,OAAA;UAAAG,QAAA,EAAI;QAAiC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1CP,OAAA;UAAAG,QAAA,EAAG;QAAsH;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1H,CAAC,eACNP,OAAA;QAAKE,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BH,OAAA;UAAAG,QAAA,EAAI;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrBP,OAAA;UAAAG,QAAA,gBACEH,OAAA;YAAAG,QAAA,EAAI;UAA+B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxCP,OAAA;YAAAG,QAAA,EAAI;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChCP,OAAA;YAAAG,QAAA,EAAI;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5BP,OAAA;YAAAG,QAAA,EAAI;UAA2B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACNP,OAAA;QAAKE,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BH,OAAA;UAAAG,QAAA,EAAI;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3BP,OAAA;UAAAG,QAAA,gBACEH,OAAA;YAAAG,QAAA,EAAI;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7BP,OAAA;YAAAG,QAAA,EAAI;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjCP,OAAA;YAAAG,QAAA,EAAI;UAAwC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjDP,OAAA;YAAAG,QAAA,EAAI;UAA0C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACNP,OAAA;QAAKE,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BH,OAAA;UAAAG,QAAA,EAAI;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnBP,OAAA;UAAAG,QAAA,EAAG;QAAqF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC5FP,OAAA;UAAGE,SAAS,EAAC,SAAS;UAAAC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNP,OAAA;MAAKE,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5BH,OAAA;QAAAG,QAAA,EAAG;MAA4E;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb;AAACC,EAAA,GArCQP,MAAM;AAuCf,eAAeA,MAAM;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}