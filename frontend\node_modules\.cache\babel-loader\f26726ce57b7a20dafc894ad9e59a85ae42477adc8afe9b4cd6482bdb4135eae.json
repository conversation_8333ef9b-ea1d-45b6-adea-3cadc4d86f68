{"ast": null, "code": "var _jsxFileName = \"D:\\\\Desktop files\\\\My New Desktop\\\\Irrigation Engineering and Structure Design\\\\frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from 'react';\nimport DrawingCanvas from './components/DrawingCanvas';\nimport GridConfig from './components/GridConfig';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [chakBoundary, setChakBoundary] = useState([]);\n  const [gridConfig, setGridConfig] = useState({\n    murabaSize: 5,\n    killaSize: 1,\n    eastWestUnit: 220,\n    northSouthUnit: 198\n  });\n  const handleBoundaryComplete = points => {\n    setChakBoundary(points);\n    // Calculate area will be implemented later\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Chak Plan Design Tool\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"main-content\",\n      children: [/*#__PURE__*/_jsxDEV(DrawingCanvas, {\n        onBoundaryComplete: handleBoundaryComplete,\n        gridConfig: gridConfig\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(GridConfig, {\n        config: gridConfig,\n        onConfigChange: setGridConfig\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this), chakBoundary.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"calculation-result\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Chak Plan Summary\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Boundary Points: \", chakBoundary.length]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"Aq0Rqvvm4e8NcKiMAerkLZNe23w=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "DrawingCanvas", "GridConfig", "jsxDEV", "_jsxDEV", "App", "_s", "chakBoundary", "setChakBoundary", "gridConfig", "setGridConfig", "murabaSize", "killa<PERSON>ize", "eastWestUnit", "northSouthUnit", "handleBoundaryComplete", "points", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onBoundaryComplete", "config", "onConfigChange", "length", "_c", "$RefreshReg$"], "sources": ["D:/Desktop files/My New Desktop/Irrigation Engineering and Structure Design/frontend/src/App.js"], "sourcesContent": ["import React, { useState, useRef } from 'react';\r\nimport DrawingCanvas from './components/DrawingCanvas';\r\nimport GridConfig from './components/GridConfig';\r\nimport './App.css';\r\n\r\nfunction App() {\r\n  const [chakBoundary, setChakBoundary] = useState([]);\r\n  const [gridConfig, setGridConfig] = useState({\r\n    murabaSize: 5,\r\n    killaSize: 1,\r\n    eastWestUnit: 220,\r\n    northSouthUnit: 198\r\n  });\r\n\r\n  const handleBoundaryComplete = (points) => {\r\n    setChakBoundary(points);\r\n    // Calculate area will be implemented later\r\n  };\r\n\r\n  return (\r\n    <div className=\"app\">\r\n      <h1>Chak Plan Design Tool</h1>\r\n      <div className=\"main-content\">\r\n        <DrawingCanvas \r\n          onBoundaryComplete={handleBoundaryComplete}\r\n          gridConfig={gridConfig}\r\n        />\r\n        <GridConfig \r\n          config={gridConfig} \r\n          onConfigChange={setGridConfig} \r\n        />\r\n      </div>\r\n      {chakBoundary.length > 0 && (\r\n        <div className=\"calculation-result\">\r\n          <h3>Chak Plan Summary</h3>\r\n          <p>Boundary Points: {chakBoundary.length}</p>\r\n          {/* Area calculation will be added later */}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default App;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC/C,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACU,UAAU,EAAEC,aAAa,CAAC,GAAGX,QAAQ,CAAC;IAC3CY,UAAU,EAAE,CAAC;IACbC,SAAS,EAAE,CAAC;IACZC,YAAY,EAAE,GAAG;IACjBC,cAAc,EAAE;EAClB,CAAC,CAAC;EAEF,MAAMC,sBAAsB,GAAIC,MAAM,IAAK;IACzCR,eAAe,CAACQ,MAAM,CAAC;IACvB;EACF,CAAC;EAED,oBACEZ,OAAA;IAAKa,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClBd,OAAA;MAAAc,QAAA,EAAI;IAAqB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC9BlB,OAAA;MAAKa,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3Bd,OAAA,CAACH,aAAa;QACZsB,kBAAkB,EAAER,sBAAuB;QAC3CN,UAAU,EAAEA;MAAW;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC,eACFlB,OAAA,CAACF,UAAU;QACTsB,MAAM,EAAEf,UAAW;QACnBgB,cAAc,EAAEf;MAAc;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EACLf,YAAY,CAACmB,MAAM,GAAG,CAAC,iBACtBtB,OAAA;MAAKa,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjCd,OAAA;QAAAc,QAAA,EAAI;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1BlB,OAAA;QAAAc,QAAA,GAAG,mBAAiB,EAACX,YAAY,CAACmB,MAAM;MAAA;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAE1C,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAAChB,EAAA,CApCQD,GAAG;AAAAsB,EAAA,GAAHtB,GAAG;AAsCZ,eAAeA,GAAG;AAAC,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}