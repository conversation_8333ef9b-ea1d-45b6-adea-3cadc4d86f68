{"ast": null, "code": "var _jsxFileName = \"D:\\\\Desktop files\\\\My New Desktop\\\\Irrigation Engineering and Structure Design\\\\frontend\\\\src\\\\components\\\\DrawingCanvas.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { MapContainer, TileLayer, Polygon, Polyline, LayersControl, useMap } from 'react-leaflet';\nimport 'leaflet/dist/leaflet.css';\nimport L from 'leaflet';\nimport 'leaflet-draw/dist/leaflet.draw.css';\nimport 'leaflet-draw';\n\n// Fix for default markers\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\ndelete L.Icon.Default.prototype._getIconUrl;\nL.Icon.Default.mergeOptions({\n  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon-2x.png',\n  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon.png',\n  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-shadow.png'\n});\nfunction DrawingCanvas({\n  onBoundaryComplete,\n  gridConfig\n}) {\n  _s();\n  const mapRef = useRef(null);\n  const [map, setMap] = useState(null);\n  const [drawControl, setDrawControl] = useState(null);\n  const [drawnItems] = useState(L.featureGroup());\n  const [polygonPoints, setPolygonPoints] = useState([]);\n  const [gridLayer, setGridLayer] = useState(null);\n\n  // Initialize map and drawing controls\n  useEffect(() => {\n    if (!map) return;\n\n    // Initialize drawing controls\n    const drawControl = new L.Control.Draw({\n      position: 'topright',\n      draw: {\n        polygon: {\n          shapeOptions: {\n            color: '#3388ff',\n            fillOpacity: 0.2\n          },\n          allowIntersection: false,\n          showArea: true\n        },\n        polyline: false,\n        circle: false,\n        rectangle: false,\n        circlemarker: false,\n        marker: false\n      },\n      edit: {\n        featureGroup: drawnItems\n      }\n    });\n    map.addControl(drawControl);\n    setDrawControl(drawControl);\n\n    // Handle drawing events\n    map.on(L.Draw.Event.CREATED, e => {\n      const layer = e.layer;\n      drawnItems.addLayer(layer);\n      if (e.layerType === 'polygon') {\n        const points = layer.getLatLngs()[0].map(point => [point.lat, point.lng]);\n        setPolygonPoints(points);\n        onBoundaryComplete(points);\n      }\n    });\n    return () => {\n      if (drawControl) map.removeControl(drawControl);\n    };\n  }, [map, onBoundaryComplete, drawnItems]);\n\n  // Render grid when polygon or config changes\n  useEffect(() => {\n    if (!map || polygonPoints.length === 0) return;\n\n    // Remove existing grid layer\n    if (gridLayer) {\n      map.removeLayer(gridLayer);\n    }\n\n    // TODO: Implement grid rendering based on polygon and config\n    // This will be added in the next iteration\n  }, [map, polygonPoints, gridConfig]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"drawing-canvas\",\n    children: /*#__PURE__*/_jsxDEV(MapContainer, {\n      center: [28.6139, 77.2090] // Default to New Delhi coordinates\n      ,\n      zoom: 15,\n      style: {\n        height: '500px',\n        width: '100%'\n      },\n      whenCreated: setMap,\n      ref: mapRef,\n      children: [/*#__PURE__*/_jsxDEV(TileLayer, {\n        url: \"https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png\",\n        attribution: \"\\xA9 <a href=\\\"https://www.openstreetmap.org/copyright\\\">OpenStreetMap</a> contributors, \\xA9 <a href=\\\"https://carto.com/attributions\\\">CARTO</a>\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this), polygonPoints.length > 0 && /*#__PURE__*/_jsxDEV(Polygon, {\n        positions: polygonPoints,\n        color: \"#3388ff\",\n        fillOpacity: 0.2\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 86,\n    columnNumber: 5\n  }, this);\n}\n_s(DrawingCanvas, \"uRzbkh4W+SXI7rb5vJqYPrv10OQ=\");\n_c = DrawingCanvas;\nexport default DrawingCanvas;\nvar _c;\n$RefreshReg$(_c, \"DrawingCanvas\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "MapContainer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Polygon", "Polyline", "LayersControl", "useMap", "L", "jsxDEV", "_jsxDEV", "Icon", "<PERSON><PERSON><PERSON>", "prototype", "_getIconUrl", "mergeOptions", "iconRetinaUrl", "iconUrl", "shadowUrl", "DrawingCanvas", "onBoundaryComplete", "gridConfig", "_s", "mapRef", "map", "setMap", "drawControl", "setDrawControl", "drawnItems", "featureGroup", "polygonPoints", "setPolygonPoints", "gridLayer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Control", "Draw", "position", "draw", "polygon", "shapeOptions", "color", "fillOpacity", "allowIntersection", "showArea", "polyline", "circle", "rectangle", "circlemarker", "marker", "edit", "addControl", "on", "Event", "CREATED", "e", "layer", "add<PERSON><PERSON>er", "layerType", "points", "getLatLngs", "point", "lat", "lng", "removeControl", "length", "<PERSON><PERSON><PERSON>er", "className", "children", "center", "zoom", "style", "height", "width", "whenCreated", "ref", "url", "attribution", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "positions", "_c", "$RefreshReg$"], "sources": ["D:/Desktop files/My New Desktop/Irrigation Engineering and Structure Design/frontend/src/components/DrawingCanvas.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\r\nimport { <PERSON><PERSON>ontainer, TileLayer, Polygon, Polyline, LayersControl, useMap } from 'react-leaflet';\r\nimport 'leaflet/dist/leaflet.css';\r\nimport L from 'leaflet';\r\nimport 'leaflet-draw/dist/leaflet.draw.css';\r\nimport 'leaflet-draw';\r\n\r\n// Fix for default markers\r\ndelete L.Icon.Default.prototype._getIconUrl;\r\nL.Icon.Default.mergeOptions({\r\n  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon-2x.png',\r\n  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon.png',\r\n  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-shadow.png',\r\n});\r\n\r\nfunction DrawingCanvas({ onBoundaryComplete, gridConfig }) {\r\n  const mapRef = useRef(null);\r\n  const [map, setMap] = useState(null);\r\n  const [drawControl, setDrawControl] = useState(null);\r\n  const [drawnItems] = useState(L.featureGroup());\r\n  const [polygonPoints, setPolygonPoints] = useState([]);\r\n  const [gridLayer, setGridLayer] = useState(null);\r\n\r\n  // Initialize map and drawing controls\r\n  useEffect(() => {\r\n    if (!map) return;\r\n\r\n    // Initialize drawing controls\r\n    const drawControl = new L.Control.Draw({\r\n      position: 'topright',\r\n      draw: {\r\n        polygon: {\r\n          shapeOptions: {\r\n            color: '#3388ff',\r\n            fillOpacity: 0.2\r\n          },\r\n          allowIntersection: false,\r\n          showArea: true\r\n        },\r\n        polyline: false,\r\n        circle: false,\r\n        rectangle: false,\r\n        circlemarker: false,\r\n        marker: false\r\n      },\r\n      edit: {\r\n        featureGroup: drawnItems\r\n      }\r\n    });\r\n\r\n    map.addControl(drawControl);\r\n    setDrawControl(drawControl);\r\n\r\n    // Handle drawing events\r\n    map.on(L.Draw.Event.CREATED, (e) => {\r\n      const layer = e.layer;\r\n      drawnItems.addLayer(layer);\r\n\r\n      if (e.layerType === 'polygon') {\r\n        const points = layer.getLatLngs()[0].map(point => [point.lat, point.lng]);\r\n        setPolygonPoints(points);\r\n        onBoundaryComplete(points);\r\n      }\r\n    });\r\n\r\n    return () => {\r\n      if (drawControl) map.removeControl(drawControl);\r\n    };\r\n  }, [map, onBoundaryComplete, drawnItems]);\r\n\r\n  // Render grid when polygon or config changes\r\n  useEffect(() => {\r\n    if (!map || polygonPoints.length === 0) return;\r\n\r\n    // Remove existing grid layer\r\n    if (gridLayer) {\r\n      map.removeLayer(gridLayer);\r\n    }\r\n\r\n    // TODO: Implement grid rendering based on polygon and config\r\n    // This will be added in the next iteration\r\n\r\n  }, [map, polygonPoints, gridConfig]);\r\n\r\n  return (\r\n    <div className=\"drawing-canvas\">\r\n      <MapContainer\r\n        center={[28.6139, 77.2090]}  // Default to New Delhi coordinates\r\n        zoom={15}\r\n        style={{ height: '500px', width: '100%' }}\r\n        whenCreated={setMap}\r\n        ref={mapRef}\r\n      >\r\n        <TileLayer\r\n          url=\"https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png\"\r\n          attribution='&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors, &copy; <a href=\"https://carto.com/attributions\">CARTO</a>'\r\n        />\r\n        {polygonPoints.length > 0 && (\r\n          <Polygon\r\n            positions={polygonPoints}\r\n            color=\"#3388ff\"\r\n            fillOpacity={0.2}\r\n          />\r\n        )}\r\n      </MapContainer>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default DrawingCanvas;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,YAAY,EAAEC,SAAS,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,MAAM,QAAQ,eAAe;AACjG,OAAO,0BAA0B;AACjC,OAAOC,CAAC,MAAM,SAAS;AACvB,OAAO,oCAAoC;AAC3C,OAAO,cAAc;;AAErB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,OAAOF,CAAC,CAACG,IAAI,CAACC,OAAO,CAACC,SAAS,CAACC,WAAW;AAC3CN,CAAC,CAACG,IAAI,CAACC,OAAO,CAACG,YAAY,CAAC;EAC1BC,aAAa,EAAE,gFAAgF;EAC/FC,OAAO,EAAE,6EAA6E;EACtFC,SAAS,EAAE;AACb,CAAC,CAAC;AAEF,SAASC,aAAaA,CAAC;EAAEC,kBAAkB;EAAEC;AAAW,CAAC,EAAE;EAAAC,EAAA;EACzD,MAAMC,MAAM,GAAGtB,MAAM,CAAC,IAAI,CAAC;EAC3B,MAAM,CAACuB,GAAG,EAAEC,MAAM,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EACpC,MAAM,CAAC2B,WAAW,EAAEC,cAAc,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC6B,UAAU,CAAC,GAAG7B,QAAQ,CAACS,CAAC,CAACqB,YAAY,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACiC,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;;EAEhD;EACAC,SAAS,CAAC,MAAM;IACd,IAAI,CAACwB,GAAG,EAAE;;IAEV;IACA,MAAME,WAAW,GAAG,IAAIlB,CAAC,CAAC0B,OAAO,CAACC,IAAI,CAAC;MACrCC,QAAQ,EAAE,UAAU;MACpBC,IAAI,EAAE;QACJC,OAAO,EAAE;UACPC,YAAY,EAAE;YACZC,KAAK,EAAE,SAAS;YAChBC,WAAW,EAAE;UACf,CAAC;UACDC,iBAAiB,EAAE,KAAK;UACxBC,QAAQ,EAAE;QACZ,CAAC;QACDC,QAAQ,EAAE,KAAK;QACfC,MAAM,EAAE,KAAK;QACbC,SAAS,EAAE,KAAK;QAChBC,YAAY,EAAE,KAAK;QACnBC,MAAM,EAAE;MACV,CAAC;MACDC,IAAI,EAAE;QACJpB,YAAY,EAAED;MAChB;IACF,CAAC,CAAC;IAEFJ,GAAG,CAAC0B,UAAU,CAACxB,WAAW,CAAC;IAC3BC,cAAc,CAACD,WAAW,CAAC;;IAE3B;IACAF,GAAG,CAAC2B,EAAE,CAAC3C,CAAC,CAAC2B,IAAI,CAACiB,KAAK,CAACC,OAAO,EAAGC,CAAC,IAAK;MAClC,MAAMC,KAAK,GAAGD,CAAC,CAACC,KAAK;MACrB3B,UAAU,CAAC4B,QAAQ,CAACD,KAAK,CAAC;MAE1B,IAAID,CAAC,CAACG,SAAS,KAAK,SAAS,EAAE;QAC7B,MAAMC,MAAM,GAAGH,KAAK,CAACI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAACnC,GAAG,CAACoC,KAAK,IAAI,CAACA,KAAK,CAACC,GAAG,EAAED,KAAK,CAACE,GAAG,CAAC,CAAC;QACzE/B,gBAAgB,CAAC2B,MAAM,CAAC;QACxBtC,kBAAkB,CAACsC,MAAM,CAAC;MAC5B;IACF,CAAC,CAAC;IAEF,OAAO,MAAM;MACX,IAAIhC,WAAW,EAAEF,GAAG,CAACuC,aAAa,CAACrC,WAAW,CAAC;IACjD,CAAC;EACH,CAAC,EAAE,CAACF,GAAG,EAAEJ,kBAAkB,EAAEQ,UAAU,CAAC,CAAC;;EAEzC;EACA5B,SAAS,CAAC,MAAM;IACd,IAAI,CAACwB,GAAG,IAAIM,aAAa,CAACkC,MAAM,KAAK,CAAC,EAAE;;IAExC;IACA,IAAIhC,SAAS,EAAE;MACbR,GAAG,CAACyC,WAAW,CAACjC,SAAS,CAAC;IAC5B;;IAEA;IACA;EAEF,CAAC,EAAE,CAACR,GAAG,EAAEM,aAAa,EAAET,UAAU,CAAC,CAAC;EAEpC,oBACEX,OAAA;IAAKwD,SAAS,EAAC,gBAAgB;IAAAC,QAAA,eAC7BzD,OAAA,CAACR,YAAY;MACXkE,MAAM,EAAE,CAAC,OAAO,EAAE,OAAO,CAAE,CAAE;MAAA;MAC7BC,IAAI,EAAE,EAAG;MACTC,KAAK,EAAE;QAAEC,MAAM,EAAE,OAAO;QAAEC,KAAK,EAAE;MAAO,CAAE;MAC1CC,WAAW,EAAEhD,MAAO;MACpBiD,GAAG,EAAEnD,MAAO;MAAA4C,QAAA,gBAEZzD,OAAA,CAACP,SAAS;QACRwE,GAAG,EAAC,gEAAgE;QACpEC,WAAW,EAAC;MAAoJ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjK,CAAC,EACDlD,aAAa,CAACkC,MAAM,GAAG,CAAC,iBACvBtD,OAAA,CAACN,OAAO;QACN6E,SAAS,EAAEnD,aAAc;QACzBU,KAAK,EAAC,SAAS;QACfC,WAAW,EAAE;MAAI;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACW;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEV;AAAC1D,EAAA,CA5FQH,aAAa;AAAA+D,EAAA,GAAb/D,aAAa;AA8FtB,eAAeA,aAAa;AAAC,IAAA+D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}