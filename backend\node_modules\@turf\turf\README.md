# @turf/turf

# turf

Turf is a modular geospatial analysis engine written in JavaScript. It performs geospatial
processing tasks with GeoJSON data and can be run on a server or in a browser.

<!-- This file is automatically generated. Please don't edit it directly:
if you find an error, edit the source file (likely index.js), and re-run
./scripts/generate-readmes in the turf project. -->

---

This module is part of the [Turfjs project](http://turfjs.org/), an open source
module collection dedicated to geographic algorithms. It is maintained in the
[Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create
PRs and issues.

### Installation

Install this module individually:

```sh
$ npm install @turf/turf
```

Or install the Turf module that includes it as a function:

```sh
$ npm install @turf/turf
```
