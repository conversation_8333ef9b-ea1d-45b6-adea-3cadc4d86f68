{"ast": null, "code": "import { LatLngBounds } from 'leaflet';\nexport function updateMediaOverlay(overlay, props, prevProps) {\n  if (props.bounds instanceof LatLngBounds && props.bounds !== prevProps.bounds) {\n    overlay.setBounds(props.bounds);\n  }\n  if (props.opacity != null && props.opacity !== prevProps.opacity) {\n    overlay.setOpacity(props.opacity);\n  }\n  if (props.zIndex != null && props.zIndex !== prevProps.zIndex) {\n    // @ts-ignore missing in definition but inherited from ImageOverlay\n    overlay.setZIndex(props.zIndex);\n  }\n}", "map": {"version": 3, "names": ["LatLngBounds", "updateMediaOverlay", "overlay", "props", "prevProps", "bounds", "setBounds", "opacity", "setOpacity", "zIndex", "setZIndex"], "sources": ["D:/Desktop files/My New Desktop/Irrigation Engineering and Structure Design/frontend/node_modules/@react-leaflet/core/lib/media-overlay.js"], "sourcesContent": ["import { LatLngBounds } from 'leaflet';\nexport function updateMediaOverlay(overlay, props, prevProps) {\n    if (props.bounds instanceof LatLngBounds && props.bounds !== prevProps.bounds) {\n        overlay.setBounds(props.bounds);\n    }\n    if (props.opacity != null && props.opacity !== prevProps.opacity) {\n        overlay.setOpacity(props.opacity);\n    }\n    if (props.zIndex != null && props.zIndex !== prevProps.zIndex) {\n        // @ts-ignore missing in definition but inherited from ImageOverlay\n        overlay.setZIndex(props.zIndex);\n    }\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,SAAS;AACtC,OAAO,SAASC,kBAAkBA,CAACC,OAAO,EAAEC,KAAK,EAAEC,SAAS,EAAE;EAC1D,IAAID,KAAK,CAACE,MAAM,YAAYL,YAAY,IAAIG,KAAK,CAACE,MAAM,KAAKD,SAAS,CAACC,MAAM,EAAE;IAC3EH,OAAO,CAACI,SAAS,CAACH,KAAK,CAACE,MAAM,CAAC;EACnC;EACA,IAAIF,KAAK,CAACI,OAAO,IAAI,IAAI,IAAIJ,KAAK,CAACI,OAAO,KAAKH,SAAS,CAACG,OAAO,EAAE;IAC9DL,OAAO,CAACM,UAAU,CAACL,KAAK,CAACI,OAAO,CAAC;EACrC;EACA,IAAIJ,KAAK,CAACM,MAAM,IAAI,IAAI,IAAIN,KAAK,CAACM,MAAM,KAAKL,SAAS,CAACK,MAAM,EAAE;IAC3D;IACAP,OAAO,CAACQ,SAAS,CAACP,KAAK,CAACM,MAAM,CAAC;EACnC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}