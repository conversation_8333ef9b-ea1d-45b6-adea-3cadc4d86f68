# @turf/flip

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## flip

Takes input features and flips all of their coordinates from `[x, y]` to `[y, x]`.

**Parameters**

-   `geo<PERSON>son` **[GeoJSON][1]** input features
-   `options` **[Object][2]** Optional parameters (optional, default `{}`)
    -   `options.mutate` **[boolean][3]** allows GeoJSON input to be mutated (significant performance increase if true) (optional, default `false`)

**Examples**

```javascript
var serbia = turf.point([20.566406, 43.421008]);

var saudiArabia = turf.flip(serbia);

//addToMap
var addToMap = [serbia, saudiArabia];
```

Returns **[GeoJSON][1]** a feature or set of features of the same type as `input` with flipped coordinates

[1]: https://tools.ietf.org/html/rfc7946#section-3

[2]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[3]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean

<!-- This file is automatically generated. Please don't edit it directly:
if you find an error, edit the source file (likely index.js), and re-run
./scripts/generate-readmes in the turf project. -->

---

This module is part of the [Turfjs project](http://turfjs.org/), an open source
module collection dedicated to geographic algorithms. It is maintained in the
[Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create
PRs and issues.

### Installation

Install this module individually:

```sh
$ npm install @turf/flip
```

Or install the Turf module that includes it as a function:

```sh
$ npm install @turf/turf
```
